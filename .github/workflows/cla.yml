name: "CLA Assistant"
on:
  issue_comment:
    types: [created]
  pull_request_target:
    types: [opened, closed, synchronize]

jobs:
  CLAssistant:
    runs-on: ubuntu-latest
    steps:
      - name: "CLA Assistant"
        if: ( github.event.comment.body == 'recheck' || github.event.comment.body == 'I have read the CLA Document and I hereby sign the CLA' || github.event_name == 'pull_request_target' ) && github.repository == 'AssetRipper/AssetRipper'
        # Beta Release
        uses: cla-assistant/github-action@v2.6.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # the below token should have repo scope and must be manually added by you in the repository's secret
          PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
        with:
          path-to-signatures: "signatures/version1/cla.json"
          path-to-document: "https://github.com/AssetRipper/ContributorLicenseAgreement/blob/main/ContributorLicenseAgreement.md" # e.g. a CLA or a DCO document
          # branch should not be protected
          branch: "master"
          allowlist: ds5678,Weblate-Samboy-Dev,bot*

          #below are the optional inputs - If the optional inputs are not given, then default values will be taken
          remote-organization-name: AssetRipper #enter the remote organization name where the signatures should be stored (Default is storing the signatures in the same repository)
          remote-repository-name: AgreementSignatures #enter the  remote repository name where the signatures should be stored (Default is storing the signatures in the same repository)
          #create-file-commit-message: 'For example: Creating file for storing CLA Signatures'
          #signed-commit-message: 'For example: $contributorName has signed the CLA in #$pullRequestNo'
          #custom-notsigned-prcomment: 'pull request comment with Introductory message to ask new contributors to sign'
          #custom-pr-sign-comment: 'The signature to be committed in order to sign the CLA'
          #custom-allsigned-prcomment: 'pull request comment when all contributors has signed, defaults to **CLA Assistant Lite bot** All Contributors have signed the CLA.'
          #lock-pullrequest-aftermerge: false - if you don't want this bot to automatically lock the pull request after merging (default - true)
          #use-dco-flag: true - If you are using DCO instead of CLA
