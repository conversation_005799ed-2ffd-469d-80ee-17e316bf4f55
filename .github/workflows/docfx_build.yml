name: DocFX Build and Publish

on:
  workflow_dispatch:
  push:
    branches: [master]
    paths:
      - "docs/**"
  pull_request:
    branches: [master]
    paths:
      - "docs/**"
  schedule:
    - cron: "0 0 * * 0" #once per week

# The schedule event can be delayed during periods of high loads of GitHub Actions workflow runs
#
# ┌───────────── minute (0 - 59)
# │ ┌───────────── hour (0 - 23)
# │ │ ┌───────────── day of the month (1 - 31)
# │ │ │ ┌───────────── month (1 - 12 or JAN-DEC)
# │ │ │ │ ┌───────────── day of the week (0 - 6 or SUN-SAT)
# │ │ │ │ │
# │ │ │ │ │
# │ │ │ │ │
# * * * * *

jobs:
  generate-docs:
    runs-on: windows-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: 9.0.x

      - name: Setup DocFX
        uses: crazy-max/ghaction-chocolatey@v3
        with:
          args: install docfx

      - name: Install dependencies
        run: dotnet restore

      - name: DocFX Build
        working-directory: docs
        run: docfx docfx.json
        continue-on-error: false

      - name: Publish
        if: ${{ ( github.event_name == 'push' || github.event_name == 'workflow_dispatch' || github.event_name == 'schedule' ) && github.repository == 'AssetRipper/AssetRipper' }}
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: docs/_site
          force_orphan: true
