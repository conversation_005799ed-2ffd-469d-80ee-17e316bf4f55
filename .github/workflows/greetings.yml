name: Greetings

on: [pull_request, issues]

jobs:
  greeting:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/first-interaction@v1
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          issue-message: "Thank you for submitting your first issue here. Please be sure you have uploaded your `AssetRipper.log` file. It is in the same folder as the exe file. :)"
          pr-message: "Thank you for making your first pull request! We greatly appreciate your enthusiam to contribute and will look at this as soon as we can."
