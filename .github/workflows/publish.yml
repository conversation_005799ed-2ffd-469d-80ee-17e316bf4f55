name: Publish

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]
  workflow_dispatch:

jobs:
  publish:
    runs-on: ${{ matrix.config.os }}

    strategy:
      matrix:
        config:
          - { name: win_x64, os: windows-latest, runtime: win-x64, executable: AssetRipper.GUI.Free.exe }
          - { name: win_arm64, os: windows-latest, runtime: win-arm64, executable: AssetRipper.GUI.Free.exe }
          - { name: linux_x64, os: ubuntu-22.04, runtime: linux-x64, executable: AssetRipper.GUI.Free }
          - { name: linux_arm64, os: ubuntu-22.04, runtime: linux-arm64, executable: AssetRipper.GUI.Free }
          - { name: mac_x64, os: macos-latest, runtime: osx-x64, executable: AssetRipper.GUI.Free }
          - { name: mac_arm64, os: macos-latest, runtime: osx-arm64, executable: AssetRipper.GUI.Free }

    steps:
      - uses: actions/checkout@v4
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: 9.0.x

      # https://learn.microsoft.com/en-us/dotnet/core/deploying/native-aot/cross-compile#linux
      - name: Install Arm64 Dependencies
        if: matrix.config.runtime == 'linux-arm64'
        run: |
          sudo dpkg --add-architecture arm64
          sudo bash -c 'cat > /etc/apt/sources.list.d/arm64.list <<EOF
          deb [arch=arm64] http://ports.ubuntu.com/ubuntu-ports/ jammy main restricted
          deb [arch=arm64] http://ports.ubuntu.com/ubuntu-ports/ jammy-updates main restricted
          deb [arch=arm64] http://ports.ubuntu.com/ubuntu-ports/ jammy-backports main restricted universe multiverse
          EOF'
          sudo sed -i -e 's/deb http/deb [arch=amd64] http/g' /etc/apt/sources.list
          sudo sed -i -e 's/deb mirror/deb [arch=amd64] mirror/g' /etc/apt/sources.list
          sudo apt update
          sudo apt install -y clang llvm binutils-aarch64-linux-gnu gcc-aarch64-linux-gnu zlib1g-dev:arm64

      - name: Publish
        run: dotnet publish -c Release -r ${{ matrix.config.runtime }}
        working-directory: ./Source/AssetRipper.GUI.Free/

      - name: List Files
        shell: bash
        run: ls -R ./Source/0Bins/AssetRipper.GUI.Free/Release/

      - name: List Files
        shell: bash
        run: date -u > ./Source/0Bins/AssetRipper.GUI.Free/Release/${{ matrix.config.runtime }}/publish/compile_time.txt

      - name: Upload
        uses: actions/upload-artifact@v4
        with:
          name: AssetRipper_${{ matrix.config.name }}
          path: |
            ./Source/0Bins/AssetRipper.GUI.Free/Release/${{ matrix.config.runtime }}/publish/${{ matrix.config.executable }}
            ./Source/0Bins/AssetRipper.GUI.Free/Release/${{ matrix.config.runtime }}/publish/*.dll
            ./Source/0Bins/AssetRipper.GUI.Free/Release/${{ matrix.config.runtime }}/publish/*.so
            ./Source/0Bins/AssetRipper.GUI.Free/Release/${{ matrix.config.runtime }}/publish/*.dylib
            ./Source/0Bins/AssetRipper.GUI.Free/Release/${{ matrix.config.runtime }}/publish/compile_time.txt
          if-no-files-found: error
