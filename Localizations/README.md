# Localizations

## How to submit translations

We have a translation platform for those of you who speak another language to work on translating the GUI into your native language. You can get started at https://weblate.samboy.dev/engage/assetripper/.

You'll need to sign up to start translating to a new language, or you can suggest translations for an existing language without an account (but someone with an account will have to manually verify your suggestions). This will send an email to the address you provide. Be sure check your spam folder, as Gmail can sometimes put the emails there.

## Licensing

Unlike the rest of AssetRipper, localizations are MIT-licensed for ease of contributing.
