{"asset_tab_audio": "Audio", "asset_tab_audio_duration_unknown": "<Unbekannt>", "asset_tab_audio_pause": "Pausieren", "asset_tab_audio_play": "Abspielen", "asset_tab_basic": "Allgemeine Info", "asset_tab_image": "Bild", "asset_tab_text": "Text", "asset_tab_yaml": "Yaml", "audio_export_title": "Audio-Exportformat", "audio_format_default": "Standard", "audio_format_default_description": "Exportiert Assets als den in die FSB-Datei eingebetteten Inhaltstyp. Die meisten Audiotypen werden als WAV exportiert, einige als OGG.", "audio_format_force_wav": "In WAV konvertieren", "audio_format_force_wav_description": "Konvertiert alle Audiodateien in WAV-Dateien. Nicht empfohlen für den Import in Unity, da die Dateien möglicherweise neu komprimiert werden, was zu Qualitätsverlusten führt.", "audio_format_native": "Raw (rohformat)", "audio_format_native_description": "„Rohes“ FSB-Audioformat. Kann nicht in Unity importiert werden, sollte also nur von fortgeschrittenen Benutzern verwendet werden.", "audio_format_yaml": "Yaml", "audio_format_yaml_description": "Export als yaml-Asset und resS-Datei. Dies ist eine sichere Option und wird als Backup verwendet, wenn etwas schief geht.", "bundled_assets_export_direct_export": "Direkter Export", "bundled_assets_export_direct_export_description": "Assets in Bundles werden ohne Gruppierung exportiert.", "bundled_assets_export_group_by_asset_type": "<PERSON><PERSON>-<PERSON>p gruppieren", "bundled_assets_export_group_by_asset_type_description": "Assets in Bundles werden behandelt, wie Assets aus anderen <PERSON>.", "bundled_assets_export_group_by_bundle_name": "<PERSON><PERSON>undle-Name gruppieren", "bundled_assets_export_group_by_bundle_name_description": "Assets in Bundles werden nach dem Namen ihres Asset-Bundles gruppiert.", "bundled_assets_export_title": "Bundled Assets Exportmodus", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "Automatisch - Experimentell", "c_sharp_langage_version_config_auto_safe": "Automatisch - Sicher", "c_sharp_langage_version_config_latest": "Aktuellste C#-Version", "c_sharp_language_version_config_description": "Die C#-<PERSON><PERSON><PERSON><PERSON>, die beim Dekompilieren von Skripts verwendet wird.", "check_log_for_more_details": "Weitere Details stehen im Protokoll geschrieben", "config_options": "Konfigurationsoptionen", "config_screen_drag_drop_prompt": "Bitte beachten: einige Einstellungsänderungen können Fehler verursachen oder verhindern.\nZum Fortfahren: Spiel-Datei bzw. -Ordner per Drag-and-Drop in dieses Fenster ziehen oder per Menü oben links manuell öffnen.", "enable_prefab_outlining": "Prefab-Gliederung aktivieren", "enable_static_mesh_separation": "Aufteilung statischer Meshes aktivieren", "error": "<PERSON><PERSON>", "error_exporting_with_reason": "Export von Spielinhalten fehlgeschlagen: {0}", "error_importing_with_reason": "<PERSON><PERSON> beim <PERSON> von <PERSON>: {0}", "export_complete": "Export vollständig abgeschlossen!", "export_deleting_old_files": "Vorhandene Dateien werden entfernt …", "export_in_progress": "Asset-<PERSON><PERSON> we<PERSON> export<PERSON>t\n{0} %\n{1}/{2}", "export_in_progress_no_file_count_yet": "Asset-Dateien werden exportiert\n0.0 % \n?/?", "export_preparing": "Export wird vorbereitet …\nDie<PERSON> könnte ein paar <PERSON>uten dauern.", "ignore_engine_assets": "Engine Assets ignorieren", "image_export_title": "Bild-Exportformat", "image_format_description": "Wirkt sich auf alle exportierten Bilder aus", "loading_game_content_from": "Spielinhalte werden geladen {0}\n{1}", "loading_step_begin_scheme_processing": "Schema-Verarbeitung beginnt", "loading_step_create_file_collection": "Dateisammlung wird erstellet", "loading_step_detect_platform": "Dateien werden Gesammelt und die Spielstruktur wird identifiziert", "loading_step_generate_dummy_dll": "Mono-Assemblies werden aus IL2CPP generiert", "loading_step_initialize_layout": "Asset-Layout wird initialisiert", "loading_step_load_assemblies": "Assemblies werden geladen", "loading_step_load_assets_from_file": "Assets aus {0} werden geladen", "loading_step_locate_key_functions": "IL2CPP-Binärdatei wird nach Bibliotheken-Funktionen durchsucht", "loading_step_parse_archive": "Archivdatei {0} wird analysiert", "loading_step_parse_bundle": "Bundle {0} wird analysiert", "loading_step_parse_il2cpp_metadata": "IL2CPP-Metadaten werden analysiert", "loading_step_parse_resource": "Ressourcendatei {0} wird analysiert", "loading_step_parse_serialized": "Serialisierte Datei {0} wird analysiert", "loading_step_parse_web": "Webdatei {0} wird analysiert", "loading_step_pre_processing": "Dateien werden vorverarbeitet", "loading_step_restore_attributes": "Attributen in generierten Assemblies werden wiederhergestellt", "loose_file_saved_at": "Lose Datei gespeichert unter {0}", "menu_export": "Exportieren", "menu_export_all": "Alle Dateien exportieren", "menu_export_selected": "Ausgewählte Datei exportieren", "menu_export_selected_type": "Alle Dateien des ausgewählten Typs exportieren", "menu_file": "<PERSON><PERSON>", "menu_file_exit": "<PERSON>den", "menu_file_open_file": "<PERSON><PERSON>", "menu_file_open_folder": "Ordner öffnen", "menu_file_reset": "Z<PERSON>ücksetzen", "menu_language": "<PERSON><PERSON><PERSON>", "mesh_export_title": "Mesh-Exportformat", "mesh_format_glb": "GLB", "mesh_format_glb_description": "Eine hochwertige Alternative zu FBX, die Open Source ist. Binäre Version von GLTF. Enthält nur Mesh-Daten. Kann Fehler verursachen. Unity kann Assets dieses Typs nicht importieren.", "mesh_format_native": "Yaml", "mesh_format_native_description": "Ein robustes Dateiformat für Meshes im Editor. Kann von einer Vielzahl von Unity-Paketen in andere Formate konvertiert werden.", "no_game_files_found": "<PERSON>in <PERSON>-Spiel oder Asset-Bundle wurde in den bereitgestellten Dateien gefunden.", "not_implemented_yet": "Noch nicht implementiert", "script_content_level_0": "Level 0", "script_content_level_0_description": "Scripts werden nicht exportiert.", "script_content_level_1": "Level 1", "script_content_level_1_description": "Methoden werden von dekompilierten Export entfernt.", "script_content_level_2": "Level 2", "script_content_level_2_description": "Standard. Exportiert vollständige Methoden für Mono-Spiele und Dummy-Methoden für IL2Cpp-Spiele.", "script_content_level_3": "Level 3", "script_content_level_3_description": "IL2Cpp-Methoden werden sicher wiederhergestellt, wenn möglich.", "script_content_level_4": "Level 4", "script_content_level_4_description": "IL2Cpp-Methoden werden ohne Rücksicht auf Erfolgswahrscheinlichkeit wiederhergestellt.", "script_content_level_title": "Skript-Inhaltslevel", "script_export_format_decompiled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "script_export_format_decompiled_description": "Der ILSpy-Decompiler wird zur Erstellung von CS-Skripten verwendet. Zuverlässige Methode. Allerdings auch zeitaufwändig und enthält viele Kompilierfehler.", "script_export_format_dll_with_renaming": "DLL-Export mit Umbenennung", "script_export_format_dll_without_renaming": "DLL-Export ohne Umbenennung", "script_export_format_dll_without_renaming_description": "Assemblies werden in ihrer kompilierten Dll-Form exportiert. Unity wird wahrscheinlich spezielle Assemblies wie Assembly-CSharp überschreiben.", "script_export_format_hybrid": "Hybrid", "script_export_format_hybrid_description": "Spezielle Assemblies wie Assembly-CSharp werden dekompiliert. Andere Assemblies werden in ihrer kompilierten Dll-Form exportiert.", "script_export_title": "Skript-Exportformat", "script_language_version_title": "C#-Sprachversion", "select_asset_to_view_info": "Asset links aus<PERSON><PERSON><PERSON><PERSON>, um die Informationen darüber anzuzeigen", "shader_asset_export_title": "Shader-Exportformat", "shader_asset_format_decompile": "Dekompilieren", "shader_asset_format_decompile_description": "Exportiert den Shader zu ShaderLab. Höchstexperimentell, führt wahrscheinlich zu Fehlern. Unterstützt nur DX11, nicht DX9.", "shader_asset_format_disassembly": "Disassemblierung", "shader_asset_format_disassembly_description": "Export der Shader als Disassemblierung. Experimentell und Fehleranfällig. Lässt sich nicht im Editor kompilieren.", "shader_asset_format_dummy": "Dummy-Shader", "shader_asset_format_dummy_description": "Export der Shader als Dummy-Shader. <PERSON><PERSON><PERSON><PERSON> dies Daten wie die Eigenschaften und FallBack beibehält, wird allgemeiner, farbdeckender Shader-Code verwendet.", "shader_asset_format_yaml": "Yaml-<PERSON><PERSON>", "shader_asset_format_yaml_description": "Export der Shader als Yaml-Asset. Experimentell und nur zur Ansicht im Editor verwendbar. Der Editor kann diese Dateien unvorhersehbar beschädigen.", "skip_streaming_assets": "StreamingAssets-Ordner überspringen", "sprite_export_title": "Sprite-Exportformat", "sprite_format_native": "Unity", "sprite_format_native_description": "Im Sprite-Format von Unity exportieren. Kann nicht außerhalb von Unity angezeigt werden.", "sprite_format_texture": "Textur", "sprite_format_texture_description": "Export als Bild des Sprite-Sheets. Kann außer<PERSON>b von Unity angezeigt werden, der Export ist jedoch langsamer.", "sprite_format_yaml": "Yaml", "sprite_format_yaml_description": "Export als yaml-Assets, die im Editor angezeigt werden können. Dies ist der einzige Modus, der eine präzise Wiederherstellung aller Metadaten von Sprites gewährleistet.", "success": "Erfolgreich!", "terrain_export_title": "Terrain-Exportformat", "terrain_format_heatmap": "Heightmap", "terrain_format_heatmap_description": "Export einer Heatmap der Höhe des Terrains in allen Bereichen. <PERSON><PERSON> dann n<PERSON>tz<PERSON>, wenn Details unwichtig sind oder das Gelände nicht als 3D-Modell benötigt wird.", "terrain_format_mesh": "3D-Mesh", "terrain_format_mesh_description": "Export des Terrains als 3D-Mesh im GLB-Format, das mit einer Vielzahl von 3D-Editoren bearbeitet werden kann.", "terrain_format_native": "Yaml", "terrain_format_native_description": "Export im nativen Terrain-Format von Unity. Die nützlichste Option, wenn zurück in Unity hineinimportiert werden soll.", "text_asset_export_title": "TextAsset-Exportformat", "text_asset_format_binary": "Bytes", "text_asset_format_binary_description": "Export der reinen Bytes des Text-Assets mit der Erweiterung .bytes.", "text_asset_format_parse": "<PERSON><PERSON>", "text_asset_format_parse_description": "Export als einfache Textdatei, j<PERSON><PERSON> soll versucht werden, die richtige Dateierweiterung zu erraten (z. B. erhalten JSON-Dateien die Erweiterung .json)", "text_asset_format_text": "Einfacher Text", "text_asset_format_text_description": "Export als eine einfache Textdatei (.txt)", "welcome_title": "<PERSON><PERSON><PERSON><PERSON> beim <PERSON>", "an_error_occured_during_decompilation": "Bei der Dekompilierung ist ein Fehler aufgetreten.", "appreciation_message": "<PERSON><PERSON>, dass Sie AssetRipper unterstützen!", "asset_tab_dependencies": "Abhängigkeiten", "asset_tab_font": "<PERSON><PERSON><PERSON><PERSON>", "assembly_name": "Assembly Name", "asset_bundle_name": "Asset Bundle Name", "asset_tab_development": "Entwicklung", "asset_tab_information": "Informationen"}