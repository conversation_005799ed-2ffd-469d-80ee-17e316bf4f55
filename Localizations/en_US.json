{"an_error_occured_during_decompilation": "An error occurred during decompilation.", "appreciation_message": "Thank you for supporting <PERSON><PERSON><PERSON><PERSON><PERSON>!", "assembly_name": "Assembly Name", "asset_bundle_name": "Asset Bundle Name", "asset_ripper_free": "AssetRipper Free", "asset_ripper_premium": "AssetRipper Premium", "asset_tab_audio": "Audio", "asset_tab_audio_duration_unknown": "<Unknown>", "asset_tab_audio_pause": "Pause", "asset_tab_audio_play": "Play", "asset_tab_dependencies": "Dependencies", "asset_tab_development": "Development", "asset_tab_font": "Font", "asset_tab_hex": "Hex", "asset_tab_image": "Image", "asset_tab_information": "Information", "asset_tab_model": "Model", "asset_tab_text": "Text", "asset_tab_video": "Video", "assets": "Assets", "audio_clip": "AudioClip", "audio_export_title": "Audio Export Format", "audio_format_default": "<PERSON><PERSON><PERSON>", "audio_format_default_description": "Export assets as the content type embedded inside the FSB. Most audio types are exported as WAV, some are exported as OGG.", "audio_format_force_wav": "Convert to WAV", "audio_format_force_wav_description": "Convert all audio files to WAV files. Not recommended if importing into Unity, as it may recompress files, causing a loss of quality.", "audio_format_native": "Raw", "audio_format_native_description": "Raw FSB Audio. Cannot be imported into Unity, so only use this if you're an advanced user.", "audio_format_yaml": "Yaml", "audio_format_yaml_description": "Export as a yaml asset and resS file. This is a safe option and is the backup when things go wrong.", "bundle": "Bundle", "bundled_assets_export_direct_export": "Direct Export", "bundled_assets_export_direct_export_description": "Bundled assets are exported without grouping.", "bundled_assets_export_group_by_asset_type": "Group By Asset Type", "bundled_assets_export_group_by_asset_type_description": "Bundled assets are treated the same as assets from other files.", "bundled_assets_export_group_by_bundle_name": "Group By Bundle Name", "bundled_assets_export_group_by_bundle_name_description": "Bundled assets are grouped by their asset bundle name.", "bundled_assets_export_title": "Bundled Assets Export Mode", "bundles": "Bundles", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_12_0": "C# 12", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "Automatic - Experimental", "c_sharp_langage_version_config_auto_safe": "Automatic - Safe", "c_sharp_langage_version_config_latest": "C# Latest", "c_sharp_language_version_config_description": "The C# language version to be used when decompiling scripts.", "channels": "Channels", "check_log_for_more_details": "Check log for more details", "class": "Class", "class_id_type_name": "Class ID Type Name", "class_id_type_number": "Class ID Type Number", "class_name": "Class Name", "collection": "Collection", "collections": "Collections", "commands": "Commands", "config_options": "Configuration Options", "config_screen_drag_drop_prompt": "Please note that some setting changes may cause or prevent errors.\nOnce you're ready, drag-and-drop your game file/folder onto this window, or use the menu in the upper-left to open something manually.", "configuration_files": "Configuration Files", "configuration_files_lists": "Lists", "configuration_files_singletons": "Singletons", "count": "Count", "csharp_type": "C# Type", "data": "Data", "default_version": "Default Version", "donation_message": "If you like <PERSON><PERSON><PERSON><PERSON><PERSON>, please consider donating:", "enable_asset_deduplication": "Enable Asset Deduplication", "enable_prefab_outlining": "Enable Prefab Outlining", "enable_static_mesh_separation": "Enable Static Mesh Separation", "error": "Error", "error_exporting_with_reason": "Failed to export game content: {0}", "error_importing_with_reason": "Failed to load game content: {0}", "experimental": "Experimental", "export_complete": "Export Complete!", "export_deleting_old_files": "Clearing out existing files...", "export_in_progress": "Exporting Asset Files\n{0}%\n{1}/{2}", "export_in_progress_no_file_count_yet": "Exporting Asset Files\n0.0%\n?/?", "export_preparing": "Preparing for Export...\nThis might take a minute.", "export_primary_content": "Export Primary Content", "export_unity_project": "Export Unity Project", "create_subfolder": "Create Subfolder", "failed_files": "Failed Files", "format": "Format", "frequency": "Frequency", "game_object": "GameObject", "guid": "GUID", "height": "Height", "home": "Home", "image_export_title": "Image Export Format", "image_format_description": "This affects all exported images.", "json": "Json", "length": "Length", "licenses": "Licenses", "lightmap_texture_export_title": "Lightmap Texture Export Format", "lightmap_texture_format_description": "This affects all exported lightmap textures.", "load": "Load", "loading_game_content_from": "Loading Game Content From {0}\n{1}", "loading_step_begin_scheme_processing": "Starting Scheme Processing", "loading_step_create_file_collection": "Creating File Collection", "loading_step_detect_platform": "Collecting Files and Detecting Game Structure", "loading_step_generate_dummy_dll": "Generating Mono Assemblies from IL2Cpp", "loading_step_initialize_layout": "Initializing Asset Layout", "loading_step_load_assemblies": "Loading Assemblies", "loading_step_load_assets_from_file": "Loading Assets from {0}", "loading_step_locate_key_functions": "Scanning IL2Cpp Binary for Library Functions", "loading_step_parse_archive": "Parsing Archive File {0}", "loading_step_parse_bundle": "Parsing Bundle {0}", "loading_step_parse_il2cpp_metadata": "Parsing IL2Cpp Metadata", "loading_step_parse_resource": "Parsing Resource File {0}", "loading_step_parse_serialized": "Parsing Serialized File {0}", "loading_step_parse_web": "Parsing Web File {0}", "loading_step_pre_processing": "Pre-Processing Files", "loading_step_restore_attributes": "Restoring Attributes on Generated Assemblies", "loose_file_saved_at": "Loose file saved at {0}", "main_asset": "Main Asset", "menu_export": "Export", "menu_export_all": "Export All Files", "menu_export_selected": "Export Selected File", "menu_file": "File", "menu_file_exit": "Exit", "menu_file_open_file": "Open File", "menu_file_open_folder": "Open Folder", "menu_file_reset": "Reset", "menu_import": "Import", "menu_language": "Language", "menu_load": "Load", "menu_view": "View", "mesh": "<PERSON><PERSON>", "mesh_export_title": "Mesh Export Format", "mesh_format_glb": "GLB", "mesh_format_glb_description": "A high-quality, open-source alternative to FBX. Binary version of GLTF. Only contains mesh data. Can cause errors. Unity cannot import assets of this type.", "mesh_format_native": "Yaml", "mesh_format_native_description": "A robust format for using meshes in the editor. Can be converted to other formats by a variety of Unity packages.", "name": "Name", "namespace": "Namespace", "no_data_has_been_loaded_for_this_key": "No data has been loaded for this key.", "no_files_loaded": "No Files Loaded", "no_game_files_found": "No Unity game or asset bundle was found in the dropped files.", "not_available_in_the_free_edition": "Not available in the free edition.", "not_implemented_yet": "Not Implemented Yet", "open_api_json": "OpenAPI JSON", "original_path": "Original Path", "override_path": "Override Path", "parent": "Parent", "path": "Path", "path_id": "Path ID", "premium_feature_notice": "Premium feature", "privacy": "Privacy", "publicize_assemblies": "Publicize Assemblies", "remove": "Remove", "remove_nullable_attributes": "Remove Nullable Attributes", "replace": "Replace", "resources": "Resources", "save": "Save", "save_raw_data": "Save Raw Data", "save_settings_to_disk": "Save Settings to Disk", "scene": "Scene", "script": "<PERSON><PERSON><PERSON>", "script_content_level_0": "Level 0", "script_content_level_0_description": "Scripts are not exported.", "script_content_level_1": "Level 1", "script_content_level_1_description": "Methods are stripped from decompiled export.", "script_content_level_2": "Level 2", "script_content_level_2_description": "Default. This exports full methods for Mono games and dummy methods for IL2Cpp games.", "script_content_level_3": "Level 3", "script_content_level_3_description": "IL2Cpp methods are safely recovered where possible.", "script_content_level_4": "Level 4", "script_content_level_4_description": "IL2Cpp methods are recovered without regard to safety.", "script_content_level_title": "Script Content Level", "script_export_format_decompiled": "Decompilation", "script_export_format_decompiled_description": "The ILSpy decompiler is used to generate CS scripts. This is reliable. However, it's also time-consuming and contains many compile errors.", "script_export_format_dll_with_renaming": "Dll Export With Renaming", "script_export_format_dll_without_renaming": "Dll Export Without Renaming", "script_export_format_dll_without_renaming_description": "Assemblies are exported in their compiled Dll form. Unity will likely overwrite special assemblies like Assembly-CSharp.", "script_export_format_hybrid": "Hybrid", "script_export_format_hybrid_description": "Special assemblies like Assembly-CSharp are decompiled. Other assemblies are exported in their compiled Dll form.", "script_export_title": "Script Export Format", "script_language_version_title": "C# Language Version", "scripts_use_fully_qualified_type_names": "Scripts use fully-qualified type names", "select_file": "Select File", "select_files": "Select Files", "select_folder": "Select Folder", "select_folders": "Select Folders", "settings": "Settings", "settings_can_only_be_changed_before_loading_files": "Settings can only be changed before loading files.", "shader": "Shader", "shader_asset_export_title": "Shader Export Format", "shader_asset_format_decompile": "Decompilation", "shader_asset_format_decompile_description": "Export the shader to ShaderLab. Very experimental and almost certainly will have errors. Only supports DX11, not DX9.", "shader_asset_format_disassembly": "Disassembly", "shader_asset_format_disassembly_description": "Export the shader as disassembly. Experimental and prone to breakage. This does not compile in the editor.", "shader_asset_format_dummy": "Dummy <PERSON>", "shader_asset_format_dummy_description": "Export the shader as a dummy shader. Although it preserves data like the properties and fallback, it uses general, opaque shader code.", "shader_asset_format_yaml": "<PERSON><PERSON><PERSON>", "shader_asset_format_yaml_description": "Export the shader as a yaml asset. Experimental and only usable for viewing in the editor. The editor can randomly corrupt these files.", "size": "Size", "skip_streaming_assets": "Skip StreamingAssets Folder", "sprite_export_title": "Sprite Export Format", "sprite_format_native": "Unity", "sprite_format_native_description": "Export in the Unity sprite format. Cannot be viewed outside of Unity.", "sprite_format_texture": "Texture", "sprite_format_texture_description": "Export as an image of the sprite sheet. Can be viewed outside of Unity, but slower to export.", "sprite_format_yaml": "Yaml", "sprite_format_yaml_description": "Export as yaml assets which can be viewed in the editor. This is the only mode that ensures a precise recovery of all metadata of sprites.", "stack_trace": "Stack Trace", "submesh_count": "Submesh Count", "success": "Success!", "swagger_documentation": "Swagger Documentation", "target_version_for_version_changing": "Target Version For Version Changing", "terrain_export_title": "Terrain Export Format", "terrain_format_heatmap": "Heightmap", "terrain_format_heatmap_description": "Export a heatmap of the height of the terrain at each location. Only really useful if you don't care about the details or having the terrain in 3D.", "terrain_format_mesh": "3D Mesh", "terrain_format_mesh_description": "Export the terrain as a 3D mesh in GLB format, suitable for viewing with a wide range of 3D editors.", "terrain_format_native": "Yaml", "terrain_format_native_description": "Export in the native Unity terrain format. Most useful option if you plan to re-import back into Unity.", "text_asset_export_title": "TextAsset Export Format", "text_asset_format_binary": "Bytes", "text_asset_format_binary_description": "Export the raw data of the text asset as a BYTES file.", "text_asset_format_parse": "Parse", "text_asset_format_parse_description": "Export as a plain text file, but try to guess the correct file extension (e.g. JSON files get the .json extension).", "text_asset_format_text": "Plain Text", "text_asset_format_text_description": "Export as a plain text file (.txt).", "texture": "Texture", "there_are_no_keys_for_this_data_type": "There are no keys for this data type.", "transform": "Transform", "vertex_count": "Vertex Count", "view_loaded_files": "View Loaded Files", "warning_this_directory_is_not_empty_all_content_will_be_deleted": "Warning: this directory is not empty. All content will be deleted.", "welcome": "Welcome", "width": "<PERSON><PERSON><PERSON>", "yaml": "Yaml"}