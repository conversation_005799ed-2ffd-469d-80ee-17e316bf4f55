{"an_error_occured_during_decompilation": "Ha ocurrido un error durante la descompilación.", "appreciation_message": "¡G<PERSON><PERSON> por apoyar AssetRipper!", "assembly_name": "Nombre del ensamblado", "asset_bundle_name": "Nombre del paquete de activos", "asset_ripper_free": "AssetRipper Free", "asset_ripper_premium": "AssetRipper Premium", "asset_tab_audio": "Audio", "asset_tab_audio_duration_unknown": "<Desconocido>", "asset_tab_audio_pause": "Pausa", "asset_tab_audio_play": "Reproducir", "asset_tab_dependencies": "Dependencias", "asset_tab_development": "Desarrollo", "asset_tab_font": "Tipografía", "asset_tab_hex": "Hex", "asset_tab_image": "Imagen", "asset_tab_information": "Información", "asset_tab_text": "Texto", "assets": "Activos", "audio_clip": "AudioClip", "audio_export_title": "Formato de exportación del audio", "audio_format_default": "Por defecto", "audio_format_default_description": "Exportar activos como el tipo de contenido incrustado dentro del FSB. La mayoría de audios se exportan como WAV, y otros como OGG", "audio_format_force_wav": "Convertir a WAV", "audio_format_force_wav_description": "Convertir todos los archivos de audio a archivos WAV. No se recomienda si se importa a Unity, ya que puede recomprimir archivos y provocar una pérdida de calidad.", "audio_format_native": "Sin procesar", "audio_format_native_description": "Audio FSB sin procesar. No se puede importar en Unity, así que úsalo solo si eres un usuario avanzado.", "audio_format_yaml": "Yaml", "audio_format_yaml_description": "Exportar como un activo yaml y resS. Esta es una opción segura y es la copia de seguridad cuando las cosas van mal", "bundle": "<PERSON><PERSON><PERSON>", "bundled_assets_export_direct_export": "Exportación directa", "bundled_assets_export_direct_export_description": "Los activos agrupados se exportan sin agrupar", "bundled_assets_export_group_by_asset_type": "Agrupar por tipo de activo", "bundled_assets_export_group_by_asset_type_description": "Los activos agrupados se tratan igual que los activos correspondientes a otros archivos", "bundled_assets_export_group_by_bundle_name": "Agrupar por nombre de paquete", "bundled_assets_export_group_by_bundle_name_description": "Los activos agrupados se agrupan por su nombre de paquete de activos", "bundled_assets_export_title": "Modo de exportación de activos agrupados", "bundles": "<PERSON><PERSON><PERSON>", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "Automatico - Experimental", "c_sharp_langage_version_config_auto_safe": "Automatico - Seguro", "c_sharp_langage_version_config_latest": "Versión de C# más reciente", "c_sharp_language_version_config_description": "La versión del lenguaje C# que se utilizará al descompilar scripts", "channels": "Canales", "check_log_for_more_details": "Comprobar registro para obtener más detalles", "class": "Clase", "class_id_type_name": "Nombre de tipo de ID de clase", "class_id_type_number": "Número de tipo de ID de clase", "class_name": "Nombre de clase", "collection": "Colección", "collections": "Colecciones", "commands": "<PERSON><PERSON><PERSON>", "config_options": "Opciones de configuración", "config_screen_drag_drop_prompt": "Por favor, ten en cuenta que algunos cambios de configuración pueden causar o evitar errores.\nUna vez que estés listo, arrastra y suelta tu archivo/carpeta del juego en esta ventana, o utiliza el menú de la parte superior izquierda para seleccionar algo manualmente.", "configuration_files": "Archivos de configuración", "configuration_files_lists": "Listas", "configuration_files_singletons": "Singletons", "count": "<PERSON><PERSON><PERSON> ", "csharp_type": "Tipo de C#", "data": "Datos", "default_version": "Versión por defecto", "donation_message": "Si te gusta <PERSON>, por favor considera donar:", "enable_prefab_outlining": "Habilitar Prefab Outlining", "error": "Error", "error_exporting_with_reason": "Error al exportar el contenido del juego: {0}", "error_importing_with_reason": "Error al cargar el contenido del juego: {0}", "experimental": "Experimental", "export_complete": "¡Exportación terminada!", "export_deleting_old_files": "Borrando archivos existentes...", "export_in_progress": "Exportando archivos de activos\n{0}%\n{1}/{2}", "export_in_progress_no_file_count_yet": "Exportando archivos de activos\n0.0%\n?/?", "export_preparing": "Preparando la exportación...\nEsto puede tomar un tiempo", "format": "Formato", "frequency": "Frecuencia", "game_object": "GameObject", "guid": "GUID", "height": "Alto", "home": "<PERSON><PERSON>o", "image_export_title": "Formato de exportación de imágenes", "image_format_description": "Esto afecta a todas las imágenes exportadas.", "json": "Json", "length": "<PERSON><PERSON><PERSON>", "licenses": "Licencias", "load": "<PERSON><PERSON>", "loading_game_content_from": "Cargando contenido del juego desde {0}\n{1}", "loading_step_begin_scheme_processing": "Iniciando procesamiento de esquema", "loading_step_create_file_collection": "Creando colección de archivos", "loading_step_detect_platform": "Reuniendo archivos y detectando la estructura del juego", "loading_step_generate_dummy_dll": "Generando Ensamblados Mono a partir de IL2Cpp", "loading_step_initialize_layout": "Inicializando orden de assets", "loading_step_load_assemblies": "Cargan<PERSON>", "loading_step_load_assets_from_file": "Cargando assets de {0}", "loading_step_locate_key_functions": "Escaneando binario IL2Cpp en busca de funciones de biblioteca", "loading_step_parse_archive": "Analizando archivo {0}", "loading_step_parse_bundle": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> {0}", "loading_step_parse_il2cpp_metadata": "Analizando metadatos IL2Cpp", "loading_step_parse_resource": "Analizando archivo de recursos {0}", "loading_step_parse_serialized": "Analizando archivo serializado {0}", "loading_step_parse_web": "Analizando archivo Web {0}", "loading_step_pre_processing": "Preprocesando archivos", "loading_step_restore_attributes": "Restaurar atributos en ensamblados generados", "loose_file_saved_at": "Archivo suelto guardado en {0}", "main_asset": "Asset principal", "menu_export": "Exportar", "menu_export_all": "Exportar todo", "menu_export_selected": "Exportar selección", "menu_file": "Archivo", "menu_file_exit": "Salir", "menu_file_open_file": "Abrir archivo", "menu_file_open_folder": "<PERSON><PERSON><PERSON>a", "menu_file_reset": "Reiniciar", "menu_import": "Importar", "menu_language": "Idioma", "menu_load": "<PERSON><PERSON>", "menu_view": "<PERSON>er", "mesh": "Mall<PERSON>", "mesh_export_title": "Formato de exportación de malla", "mesh_format_glb": "GLB", "mesh_format_glb_description": "Una alternativa de código abierto y alta calidad a FBX. Versión binaria de GLTF. Sólo contiene datos de malla. Puede causar errores. Unity no puede importar assets de este tipo.", "mesh_format_native": "Yaml", "mesh_format_native_description": "Un formato robusto para usar mallas en el editor. Puede ser convertido a otros formatos por una variedad de paquetes de Unity.", "name": "Nombre", "namespace": "Espacio de nombre<PERSON>", "no_data_has_been_loaded_for_this_key": "No se han cargado datos para esta clave.", "no_files_loaded": "No se han cargado archivos", "no_game_files_found": "No se pudo encontrar ningún juego de Unity o Asset Bundles en los archivos seleccionados.", "not_implemented_yet": "No implementado aún", "original_path": "Ruta original", "parent": "<PERSON><PERSON>", "path": "<PERSON><PERSON>", "path_id": "ID de ruta", "privacy": "Privacidad", "remove": "Eliminar", "replace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resources": "Recursos", "save": "Guardar", "scene": "Escena", "script": "<PERSON><PERSON><PERSON>", "script_content_level_0": "Nivel 0", "script_content_level_0_description": "Los scripts no son exportados.", "script_content_level_1": "Nivel 1", "script_content_level_1_description": "Los métodos son removidos de los scripts decompilados.", "script_content_level_2": "Level 2", "script_content_level_2_description": "Opción establecida por defecto. Se exportarán los métodos completos para juegos Mono y métodos ficticios para juegos IL2Cpp.", "script_content_level_3": "Nivel 3", "script_content_level_3_description": "Los métodos IL2Cpp son recuperados de forma segura, donde sea posible.", "script_content_level_4": "Nivel 4", "script_content_level_4_description": "Los métodos IL2Cpp son recuperados sin considerar seguridad.", "script_content_level_title": "Nivel de contenido de scripts", "script_export_format_decompiled": "Descompilación", "script_export_format_decompiled_description": "El descompilador ILSpy se utiliza para generar scripts CS de forma fiable. Sin embargo, también consume mucho tiempo y contiene muchos errores de compilación.", "script_export_format_dll_with_renaming": "Exportación DLL con renombrado", "script_export_format_dll_without_renaming": "Exportar DLL sin renombrar", "script_export_format_dll_without_renaming_description": "Los ensamblados se exportan en su forma DLL compilada. Es probable que Unity sobrescriba ensamblados especiales como Assembly-CSharp.", "script_export_format_hybrid": "<PERSON><PERSON><PERSON><PERSON>", "script_export_format_hybrid_description": "Los ensamblados especiales como Assembly-CSharp se descompilan. Otros ensamblados se exportan en su forma DLL compilada.", "script_export_title": "Formato de exportación de scripts", "script_language_version_title": "Versión del lenguaje C#", "select_file": "Seleccionar archivo", "select_folder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "Configuración", "settings_can_only_be_changed_before_loading_files": "Los ajustes solo se pueden cambiar antes de procesar los archivos.", "shader": "Sombre<PERSON> (Shader)", "shader_asset_export_title": "Formato de exportación del sombreador (shader)", "shader_asset_format_decompile": "Descompilación", "shader_asset_format_decompile_description": "Exporta el sombreador (shader) a ShaderLab. Muy experimental y casi seguro que tendrá errores. Sólo soporta DX11, no DX9.", "shader_asset_format_disassembly": "Desensamblado", "shader_asset_format_disassembly_description": "Exportar el sombreador (shader) como desensamblado. Función experimental y propensa a fallar. No compilable en el editor.", "shader_asset_format_dummy": "Sombreador (shader) ficticio", "shader_asset_format_dummy_description": "Exportar el sombreador (shader) como un sombreador ficticio. Aunque conserva datos como las propiedades y el sombreador alternativo, utiliza un código de sombreador general y opaco.", "shader_asset_format_yaml": "<PERSON><PERSON>", "shader_asset_format_yaml_description": "Exporta el sombreador (shader) como un activo Yaml. Experimental y sólo utilizable para ver en el editor. El editor puede corromper aleatoriamente estos archivos.", "skip_streaming_assets": "<PERSON><PERSON><PERSON> StreamingAssets", "sprite_export_title": "Formato de exportación de sprites", "sprite_format_native": "Unity", "sprite_format_native_description": "Exportar en el formato nativo de sprites de Unity. No generan imágenes, por lo que no se pueden modificar fuera de Unity.", "sprite_format_texture": "Textura", "sprite_format_texture_description": "Exportar como una imagen de la capa de sprites (Atlas). Se puede visualizar fuera de Unity, pero la exportación es más lenta.", "sprite_format_yaml": "Yaml", "sprite_format_yaml_description": "Exporta como assets Yaml que se pueden ver en el editor. Este es el único modo que garantiza una recuperación precisa de todos los metadatos de los sprites.", "submesh_count": "Conteo de submallas", "success": "¡Éxito!", "terrain_export_title": "Formato de exportación del terreno", "terrain_format_heatmap": "Mapa de Altura (Heightmap)", "terrain_format_heatmap_description": "Exportar un mapa de altura del terreno en cada ubicación. Solo es realmente útil si no te importan los detalles o tener el terreno en 3D.", "terrain_format_mesh": "Malla 3D", "terrain_format_mesh_description": "Exporta el terreno como una malla 3D en formato GLB, adecuada para su visualización con una amplia gama de editores 3D.", "terrain_format_native": "Yaml", "terrain_format_native_description": "Exporta en el formato de terreno nativo de Unity. Opción más útil si planeas reimportar de nuevo a Unity.", "text_asset_export_title": "Formato de exportación de assets de texto", "text_asset_format_binary": "Bytes", "text_asset_format_binary_description": "Exportar los bytes del asset de texto sin procesar con la extensión .bytes.", "text_asset_format_parse": "<PERSON><PERSON><PERSON>", "text_asset_format_parse_description": "Exporta como un archivo de texto sin formato, pero intenta adivinar la extensión de archivo correcta (por ejemplo, los archivos JSON obtienen la extensión .json).", "text_asset_format_text": "Texto plano", "text_asset_format_text_description": "Exportar como archivo de texto plano (.txt)", "texture": "Textura", "there_are_no_keys_for_this_data_type": "No hay claves para este tipo de datos", "transform": "Transform", "vertex_count": "Recuento de vértices", "view_loaded_files": "Ver archivos cargados", "warning_this_directory_is_not_empty_all_content_will_be_deleted": "Advertencia: este directorio no está vacío. Se eliminará todo el contenido", "welcome": "Bienvenido", "width": "<PERSON><PERSON>", "yaml": "Yaml", "size": "<PERSON><PERSON><PERSON>", "asset_tab_model": "<PERSON><PERSON>", "enable_asset_deduplication": "Activar deduplicación de Assets", "lightmap_texture_export_title": "Formato de exportación para texturas Lightmap", "lightmap_texture_format_description": "Esto afectará a todas las texturas lightmap exportadas.", "save_settings_to_disk": "Guardar configuración en el disco", "export_primary_content": "Exportar contenido principal", "export_unity_project": "Exportar proyecto de Unity", "stack_trace": "Seguimiento de pila", "enable_static_mesh_separation": "Habilitar Separación de Malla Estática", "failed_files": "Archivos que fallaron", "c_sharp_langage_version_config_12_0": "C# 12", "not_available_in_the_free_edition": "No disponible en la versión gratuita.", "asset_tab_video": "Vídeo", "open_api_json": "OpenAPI JSON", "save_raw_data": "Guardar datos en bruto", "swagger_documentation": "Documentación Swagger", "target_version_for_version_changing": "Versión de destino para cambio de versión"}