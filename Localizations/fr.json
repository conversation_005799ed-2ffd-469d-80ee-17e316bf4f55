{"asset_tab_audio": "Audio", "asset_tab_audio_duration_unknown": "<Inconnu>", "asset_tab_audio_pause": "Pause", "asset_tab_audio_play": "Lecture", "asset_tab_basic": "Informations de base", "asset_tab_image": "Image", "asset_tab_text": "Texte", "asset_tab_yaml": "Yaml", "audio_export_title": "Format d'Export pour l'Audio", "audio_format_default": "Défaut", "audio_format_default_description": "Exportez les éléments en tant que type de contenus intégrés dans le FSB. La plupart des types audio sont exportés au format WAV, certains sont exportés au format OGG.", "audio_format_force_wav": "Convertir vers WAV", "audio_format_force_wav_description": "Convertis tous les fichiers audio en fichiers WAV. Cette opération n'est pas recommandée lors de l'importation dans Unity, car elle peut recompresser les fichiers, entraînant une perte de qualité.", "audio_format_native": "B<PERSON><PERSON>", "audio_format_native_description": "Audio FSB brut. Ne peut pas être importé dans Unity, donc à seulement utiliser si tu es un utilisateur avancé.", "audio_format_yaml": "Yaml", "audio_format_yaml_description": "Exporter en tant qu'asset yaml et ficher resS. C'est une option sûr et est une sauvegarde quand les choses vont mal.", "bundled_assets_export_direct_export": "Export Direct", "bundled_assets_export_direct_export_description": "Les assets empaquetés sont exportés sans être groupés.", "bundled_assets_export_group_by_asset_type": "Grouper par type d'Assets", "bundled_assets_export_group_by_asset_type_description": "Les assets empaquetés sont traités de la même manière que les assets d'autres fichiers.", "bundled_assets_export_group_by_bundle_name": "Grouper par Noms de Paquets", "bundled_assets_export_group_by_bundle_name_description": "Les assets empaquetés sont groupés par le nom des paquets.", "bundled_assets_export_title": "Mode d'Export pour les Assets Empaquetés", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "Automatique - Expérimental", "c_sharp_langage_version_config_auto_safe": "Automatique - Sûr", "c_sharp_langage_version_config_latest": "C# Dernière version", "c_sharp_language_version_config_description": "La version du langage C# à utiliser lors de la décompilation des scripts.", "check_log_for_more_details": "Consulter le journal pour plus de détails", "config_options": "Options de Configuration", "config_screen_drag_drop_prompt": "Veuillez noter que certains changements des paramètres peuvent provoquer ou empêcher des erreurs.\nUne fois que vous êtes prêt, glissez-déposez le fichier/dossier du jeu dans cette fenêtre, ou utilisez le menu en haut à gauche pour ouvrir quelque chose manuellement.", "enable_prefab_outlining": "Activer les contours préfabriqués", "enable_static_mesh_separation": "Activer la Séparation des Mesh Statiques", "error": "<PERSON><PERSON><PERSON>", "error_exporting_with_reason": "Échec de l'exportation du contenu du jeu : {0}", "error_importing_with_reason": "Échec du chargement du contenu du jeu : {0}", "export_complete": "Exportation terminée !", "export_deleting_old_files": "Effacer les fichiers existants...", "export_in_progress": "Exportation de fichiers d'actifs\n{0}%\n{1}/{2}", "export_in_progress_no_file_count_yet": "Exportation des Fichiers Asset \n0.0%\n ?/ ?", "export_preparing": "Préparation à l'exportation...\nCela peut prendre une minute.", "ignore_engine_assets": "Ignorer les actifs du moteur", "image_export_title": "Format d'Export pour les Images", "image_format_description": "Cela affecte toutes les images exportées.", "loading_game_content_from": "Chargement du contenu du jeu à partir de {0}\n{1}", "loading_step_begin_scheme_processing": "Lancement du traitement des schémas", "loading_step_create_file_collection": "Création d'une collection de fichiers", "loading_step_detect_platform": "Collecte des fichiers et détection de la structure du jeu", "loading_step_generate_dummy_dll": "Génération d'assemblages mono à partir d'IL2Cpp", "loading_step_initialize_layout": "Initialisation de la présentation des actifs", "loading_step_load_assemblies": "Chargement des assemblages", "loading_step_load_assets_from_file": "Chargement des actifs de {0}", "loading_step_locate_key_functions": "Recherche de Fonctions Binaire dans la Bibliothèque IL2CPP", "loading_step_parse_archive": "Lecture du fichier d'archive {0}", "loading_step_parse_bundle": "Lecture du bundle {0}", "loading_step_parse_il2cpp_metadata": "Analyse des métadonnées IL2CPP", "loading_step_parse_resource": "Analy<PERSON> du fi<PERSON>er de ressources {0}", "loading_step_parse_serialized": "Analyse du fichier sérialis<PERSON> {0}", "loading_step_parse_web": "Lecture du fichier web {0}", "loading_step_pre_processing": "Fichiers de prétraitement", "loading_step_restore_attributes": "Restauration des attributs sur les assemblages générés", "loose_file_saved_at": "<PERSON>chier perdu enregistré à {0}", "menu_export": "Exporter", "menu_export_all": "Exporter tous les fichiers", "menu_export_selected": "Exporter le fichier sélectionné", "menu_export_selected_type": "Exporter tous les fichiers du type sélectionné", "menu_file": "<PERSON><PERSON><PERSON>", "menu_file_exit": "<PERSON><PERSON><PERSON>", "menu_file_open_file": "<PERSON><PERSON><PERSON><PERSON><PERSON> un fichier", "menu_file_open_folder": "O<PERSON><PERSON><PERSON>r un dossier", "menu_file_reset": "Réinitialiser", "menu_language": "<PERSON><PERSON>", "mesh_export_title": "Format d'Export pour les Mesh", "mesh_format_glb": "GLB", "mesh_format_glb_description": "Une alternative open-source de haute qualité à FBX. Version binaire de GLTF. Contient seulement des données mesh. Peut causer des erreurs. Unity ne peut pas importer d'assets de ce type.", "mesh_format_native": "Yaml", "mesh_format_native_description": "Un format robuste pour l'utilisation de maillages dans l'éditeur. Peut être converti en d'autres formats par divers packages Unity.", "no_game_files_found": "Aucun jeu Unity ou ensemble de ressources n'a été trouvé dans les fichiers déposés.", "not_implemented_yet": "Pas Encore Implémenté", "script_content_level_0": "Niveau 0", "script_content_level_0_description": "Les scripts ne sont pas exportés.", "script_content_level_1": "Niveau 1", "script_content_level_1_description": "Les méthodes sont retirées de l'exportation décompilée.", "script_content_level_2": "Niveau 2", "script_content_level_2_description": "Par défaut. Ceci exporte les méthodes complètes pour les jeux Mono et les méthodes factices pour les jeux IL2Cpp.", "script_content_level_3": "Niveau 3", "script_content_level_3_description": "Les méthodes IL2Cpp sont récupérées en toute sécurité lorsque cela est possible.", "script_content_level_4": "Niveau 4", "script_content_level_4_description": "Les méthodes IL2Cpp sont récupérées sans tenir compte de la sécurité.", "script_content_level_title": "Niveau de Contenu des Scripts", "script_export_format_decompiled": "Décompilé", "script_export_format_decompiled_description": "Le décompilateur ILSpy est utilisé pour générer des scripts CS. Il est fiable. Cependant, il prend du temps et contient de nombreuses erreurs de compilation.", "script_export_format_dll_with_renaming": "Exportation de Dll avec renommage", "script_export_format_dll_without_renaming": "Exportation en Dll sans renommage", "script_export_format_dll_without_renaming_description": "Les assemblages sont exportés sous leur forme Dll compilée. Unity écrasera probablement les assemblages spéciaux comme Assembly-CSharp.", "script_export_format_hybrid": "<PERSON>e", "script_export_title": "Format d'Export pour les Scripts", "script_language_version_title": "Version du Langage C#", "select_asset_to_view_info": "Sélectionnez un actif sur la gauche pour obtenir des informations à son sujet", "shader_asset_export_title": "Format d'Export pour les Shaders", "shader_asset_format_decompile": "Décompiler", "shader_asset_format_decompile_description": "Exporter le shader vers ShaderLab. Très expérimental, il est presque certain qu'il y aura des erreurs. Ne supporte que DX11, pas DX9.", "shader_asset_format_disassembly": "Désassemblage", "shader_asset_format_disassembly_description": "Exporter le shader en tant que désassemblage. Expérimental est susceptible de se casser. Cela ne compile pas dans l'éditeur.", "shader_asset_format_dummy": "<PERSON><PERSON> Factice", "shader_asset_format_dummy_description": "Exportez le shader en tant que shader factice. Bien qu'il préserve les données telles que les propriétés et les valeurs de secours, il utilise un code de shader général et opaque.", "shader_asset_format_yaml": "É<PERSON><PERSON>", "shader_asset_format_yaml_description": "Exporter le shader en tant que fichier yaml. Expérimental est uniquement utilisable dans l'éditeur. L'éditeur peut corrompre ces fichiers de manière aléatoire.", "skip_streaming_assets": "Passer le dossier StreamingAssets", "sprite_export_title": "Format d'Export pour les Sprites", "sprite_format_native": "Unity", "sprite_format_native_description": "Exporter au format sprite Unity. Ne peut pas être visualisé en dehors d'Unity.", "sprite_format_texture": "Texture", "sprite_format_texture_description": "Exporter sous forme d'image de la feuille de sprites. Peut être visualisé en dehors d'Unity, mais plus lent à exporter.", "sprite_format_yaml": "Yaml", "sprite_format_yaml_description": "Exporter en tant qu'élément yaml permet d'être lu dans un éditeur. C'est le seul mode qui permet une récupération précise de toutes les métadonnées des sprites.", "success": "Succès !", "terrain_export_title": "Format d'exportation du terrain", "terrain_format_heatmap": "Carte des hauteurs", "terrain_format_heatmap_description": "Exporter une heatmap des hauteurs du terrain à toutes les localisations. Seulement très utilise si vous ne faites pas gaffe aux détails ou avoir le terrain en 3D.", "terrain_format_mesh": "Mesh 3D", "terrain_format_mesh_description": "Exportez le terrain sous forme de maillage 3D au format GLB, qui peut être visualisé avec une large gamme d'éditeurs 3D.", "terrain_format_native": "Yaml", "terrain_format_native_description": "Exporter au format de terrain natif Unity. Option la plus utile si vous prévoyez de réimporter dans Unity.", "text_asset_export_title": "Format d'Export pour les TextAssets", "text_asset_format_binary": "Octets", "text_asset_format_binary_description": "Exportez les données brutes de l’élément texte sous forme de fichier BYTES.", "text_asset_format_parse": "Analyser", "text_asset_format_parse_description": "Exportez sous forme de fichier texte brut, mais essayez de deviner l'extension de fichier correcte (par exemple, les fichiers JSON obtiennent l'extension .json).", "text_asset_format_text": "Texte Brut", "text_asset_format_text_description": "Exporter sous forme de fichier texte brut (.txt).", "welcome_title": "Bienvenue Dans AssetRipper", "default_version": "Version par défaut", "height": "<PERSON><PERSON>", "script_export_format_hybrid_description": "Les assemblages spéciaux comme Assembly-CSharp sont décompilés. Les autres assemblages sont exportés sous leur forme Dll compilée.", "view_loaded_files": "Voir les fichiers chargés", "welcome": "Bienvenue", "width": "<PERSON><PERSON>", "donation_message": "Si vous aimez <PERSON>, s'il vous plaît faire un don :", "no_files_loaded": "<PERSON><PERSON><PERSON> fichier chargé", "export_primary_content": "Exporter le contenu principal", "lightmap_texture_export_title": "Format d'exportation de texture Lightmap", "an_error_occured_during_decompilation": "Une erreur s'est produite lors de la décompilation.", "appreciation_message": "Merci de soutenir AssetRipper !", "asset_ripper_free": "AssetRipper Gratuit", "asset_ripper_premium": "AssetRipper Premium", "asset_tab_dependencies": "Dépendances", "asset_tab_font": "Police d'écriture", "bundles": "Paquets", "channels": "<PERSON><PERSON><PERSON>", "length": "<PERSON><PERSON><PERSON>", "submesh_count": "Nombre de sous-mailles", "failed_files": "Fi<PERSON><PERSON>", "game_object": "Objet de jeu", "lightmap_texture_format_description": "Affecte toutes les textures de lightmap exportées.", "main_asset": "Principal <PERSON><PERSON>", "menu_import": "Importer", "menu_load": "Charger", "menu_view": "<PERSON><PERSON><PERSON><PERSON>", "mesh": "Maillage", "name": "Nom", "namespace": "Espace de noms", "no_data_has_been_loaded_for_this_key": "Aucune donnée n'a été chargée pour cette clé.", "assembly_name": "Nom de l'assembly", "asset_bundle_name": "Nom du paquet de l'Asset", "asset_tab_development": "Développement", "asset_tab_hex": "Hexadécimale", "asset_tab_information": "Information", "assets": "Assets", "audio_clip": "Extrait audio", "privacy": "Confidentialité", "resources": "Ressources", "save": "<PERSON><PERSON><PERSON><PERSON>", "asset_tab_model": "<PERSON><PERSON><PERSON><PERSON>", "bundle": "<PERSON><PERSON>", "class": "Classe", "class_id_type_name": "Nom du Type de la Classe ID", "class_id_type_number": "Numéro de la Classe ID", "class_name": "Nom de la Classe", "collection": "Collection", "collections": "Collections", "commands": "Commandes", "configuration_files": "Fichiers de configuration", "configuration_files_lists": "Listes", "configuration_files_singletons": "Seul Element", "count": "<PERSON><PERSON>er", "csharp_type": "Type C# ( CSHARP)", "data": "<PERSON><PERSON><PERSON>", "enable_asset_deduplication": "Activer la déduplication des Assets", "experimental": "Expérimentale", "format": "Format", "frequency": "<PERSON><PERSON><PERSON>", "open_api_json": "OpenAPI JSON", "original_path": "Chemin d'origine", "parent": "Parent", "path": "Chemin", "path_id": "ID du chemin", "remove": "<PERSON><PERSON><PERSON>", "replace": "<PERSON><PERSON>lace<PERSON>", "save_raw_data": "Enregistrer les données brutes", "save_settings_to_disk": "Enregistrer les paramètres sur le disque", "scene": "<PERSON><PERSON>", "script": "<PERSON><PERSON><PERSON>", "select_file": "Sé<PERSON><PERSON><PERSON> le fichier", "select_folder": "Sélectionner un dossier", "settings": "Paramètres", "settings_can_only_be_changed_before_loading_files": "Les paramètres ne peuvent être modifiés qu'avant le chargement des fichiers.", "shader": "Shader", "swagger_documentation": "Documentation de Swagger", "yaml": "Yaml", "guid": "Lignes directrices (GUID)", "home": "Accueil", "json": "Json", "licenses": "Licenses", "load": "Chargement", "export_unity_project": "Exporter le Projet Unity", "size": "<PERSON><PERSON>", "target_version_for_version_changing": "Version cible pour le changement de version", "texture": "Texture", "there_are_no_keys_for_this_data_type": "Il n'y a pas de clés pour ce type de données.", "transform": "Transformer", "vertex_count": "Nombre de sommets", "warning_this_directory_is_not_empty_all_content_will_be_deleted": "Attention : ce répertoire n'est pas vide. Tout le contenu sera supprimé."}