{"audio_export_title": "Formato di esportazione dei file audio", "audio_format_default": "Predefinito", "audio_format_default_description": "Esporta gli asset come il tipo di contenuto integrato all'interno dell'FSB. La maggior parte dei file audio viene esportata come file WAV, alcuni invece vengono esportati come file OGG.", "audio_format_force_wav": "Converti in formato WAV", "audio_format_force_wav_description": "Converti tutti i file audio in file WAV. Non raccomandato nel caso in cui si volesse importare i file su Unity in quanto alcuni file potrebbero esser ricompressi, causando così perdita di qualità.", "audio_format_native": "Non elaborato", "audio_format_native_description": "FIle audio FSB non elaborato. Non può essere importato su Unity, quindi usalo solo se sei un utente esperto.", "audio_format_yaml": "Yaml", "audio_format_yaml_description": "Esporta i file audio come asset yaml e file resS. Questa è un'opzione sicura e funge da alternativa nel caso in cui le cose non vadano per il verso giusto.", "bundled_assets_export_direct_export": "Esportazione diretta", "bundled_assets_export_direct_export_description": "Gli asset in bundle vengono esportati senza essere raggruppati.", "bundled_assets_export_group_by_asset_type": "Raggruppa per tipo di asset", "bundled_assets_export_group_by_asset_type_description": "Gli asset in bundle vengono trattati allo stesso modo degli asset provenienti da altri file.", "bundled_assets_export_group_by_bundle_name": "Raggruppa per nome del bundle", "bundled_assets_export_group_by_bundle_name_description": "Gli asset in bundle sono raggruppati in base al nome dei loro bundle.", "bundled_assets_export_title": "Modalità di esportazione delle risorse in bundle", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "Automatico - Sperimentale", "c_sharp_langage_version_config_auto_safe": "Automatico - Sicuro", "c_sharp_langage_version_config_latest": "C# ultima versione", "c_sharp_language_version_config_description": "La versione del linguaggio C# da utilizzare durante la decompilazione degli script.", "config_options": "Opzioni di configurazione", "enable_static_mesh_separation": "Attiva la separazione delle mesh statiche", "ignore_engine_assets": "Ignora gli asset dell'engine", "image_export_title": "Formato di esportazione delle immagini", "image_format_description": "Si applica a tutte le immagini esportate", "mesh_export_title": "Formato di esportazione dei modelli", "mesh_format_glb": "GLB", "mesh_format_glb_description": "Un'alternativa a FBX di alta qualità e a sorgente aperta. Una versione in codice binario di GLTF. Contiene solo i dati della mesh. Può causare errori. Unity non è in grado di importare asset di questo tipo.", "mesh_format_native": "Yaml", "mesh_format_native_description": "Un formato robusto per utilizzare le mesh nell'editor. <PERSON><PERSON><PERSON> esser convertito in altri formati da diversi pacchetti per Unity.", "script_content_level_0": "Livello 0", "script_content_level_0_description": "Gli script non vengono esportati.", "script_content_level_1": "Livello 1", "script_content_level_1_description": "I metodi vengono rimossi dall'esportazione decompilata.", "script_content_level_2": "Livello 2", "script_content_level_2_description": "Predefinito. Esporta metodi completi per i giochi Mono e metodi fittizi per i giochi IL2Cpp.", "script_content_level_3": "Livello 3", "script_content_level_3_description": "I metodi IL2Cpp vengono recuperati in modo sicuro, ove possibile.", "script_content_level_4": "Livello 4", "script_content_level_4_description": "I metodi IL2Cpp vengono recuperati senza tener conto della sicurezza.", "script_content_level_title": "Livello dei contenuti degli script", "script_export_format_decompiled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "script_export_format_decompiled_description": "Il decompilatore ILSpy viene utilizzato per generare gli script CS. È affidabile. Tuttavia, richiede molto tempo e contiene molti errori di compilazione.", "script_export_format_dll_with_renaming": "Esportazione dll con rinominazione", "script_export_format_dll_without_renaming": "Esportazione dll senza rinominazione", "script_export_format_dll_without_renaming_description": "Gli assembly vengono esportati nella loro forma dll compilata. Unity probabilmente sovrascriverà gli assembly speciali come Assembly-CSharp.", "script_export_format_hybrid": "Ibrido", "script_export_format_hybrid_description": "Gli assembly speciali, come Assembly-CSharp, vengono decompilati. Gli altri assembly vengono esportati nella loro forma dll compilata.", "script_export_title": "Formato di esportazione degli script", "script_language_version_title": "Versione del linguaggio C#", "shader_asset_export_title": "Formato di esportazione degli shader", "shader_asset_format_decompile": "Decompilate", "shader_asset_format_decompile_description": "Esportare le shader in ShaderLab. Molto sperimentale e quasi certamente produrrà errori. Supporta solo DX11, non DX9.", "shader_asset_format_disassembly": "Disassemblate", "shader_asset_format_disassembly_description": "Esporta le shader come disassemblate. Sperimentale e prono a problemi. Non vengono compilate nell'editor.", "shader_asset_format_dummy": "<PERSON><PERSON> fittizia", "shader_asset_format_dummy_description": "Esportare le shader come shader fittizie. Benché conservino i dati come le Properties e il FallBack, utilizza un codice generico di shader opaca.", "shader_asset_format_yaml": "<PERSON><PERSON>", "shader_asset_format_yaml_description": "Esporta le shader come asset yaml. Sperimentale e utilizzabile solo per la visualizzazione nell'editor. L'editor pu<PERSON> corrompere casualmente questi file.", "skip_streaming_assets": "Salta l'elaborazione della cartella StreamingAssets", "sprite_export_title": "Formato di esportazione degli sprite", "sprite_format_native": "Unity", "sprite_format_native_description": "Esporta nel formato degli sprite di Unity. Non può esser visualizzato al di fuori di Unity.", "sprite_format_texture": "Texture", "sprite_format_texture_description": "Esporta come un immagine dello Sprite Sheet. P<PERSON>ò essere visualizzato al di fuori di Unity, ma è più lento da esportare.", "sprite_format_yaml": "Yaml", "sprite_format_yaml_description": "Esporta come asset yaml che possono essere visualizzate nell'editor. Questa è l'unica modalità che assicura un recupero accurato di tutti i metadati degli sprite.", "terrain_export_title": "Formato di esportazione dei terreni", "terrain_format_heatmap": "Heightmap", "terrain_format_heatmap_description": "Esporta una mappa di calore dell'altezza del terreno in ogni posizione. Utile solo se non ti interessano i dettagli e non ti serve il terreno in 3D.", "terrain_format_mesh": "Mesh 3D", "terrain_format_mesh_description": "Esporta il terreno come una mesh 3D in formato GLB, adatto per esser visualizzato con un'ampia gamma di editor 3D.", "terrain_format_native": "Yaml", "terrain_format_native_description": "Esporta i terreni nel formato nativo di Unity. L'opzione più utile se prevedi di reimportare nuovamente i terreni su Unity.", "text_asset_export_title": "Formato di esportazione dei file di testo", "text_asset_format_binary": "Byte", "text_asset_format_binary_description": "Esporta i byte non elaborati degli asset di testo con estensione .bytes.", "text_asset_format_parse": "<PERSON><PERSON><PERSON><PERSON>", "text_asset_format_parse_description": "Esporta come file di testo non formattato, ma cerca di ipotizzare l'estensione corretta del file (ad esempio, i file JSON hanno l'estensione .json).", "text_asset_format_text": "Testo non formattato", "text_asset_format_text_description": "Esporta come file di testo non formattato (\".txt\").", "welcome_title": "Benvenuto in AssetRipper"}