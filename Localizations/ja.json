{"asset_tab_audio": "Audio", "asset_tab_audio_duration_unknown": "不明な形式", "asset_tab_audio_pause": "停止", "asset_tab_audio_play": "再生", "asset_tab_basic": "基本情報", "asset_tab_image": "Image", "asset_tab_text": "Text", "asset_tab_yaml": "Yaml", "audio_export_title": "Audio出力形式", "audio_format_default": "デフォルト", "audio_format_default_description": "FSBに埋め込まれた形式でエクスポートします。大抵の場合はWAVとして、一部はOGGとしてエクスポートされます。", "audio_format_force_wav": "WAVに変換", "audio_format_force_wav_description": "全てのオーディオファイルをWAVに変換します。", "audio_format_native": "Raw", "audio_format_native_description": "生のFSBファイルをエクスポートします。", "audio_format_yaml": "Yaml", "audio_format_yaml_description": "yamlとresSファイルとしてエクスポートします。安定したオプションです。", "bundled_assets_export_direct_export": "Direct Export", "bundled_assets_export_direct_export_description": "アセットバンドルをグループ化せずにエクスポートします。", "bundled_assets_export_group_by_asset_type": "アセットタイプでグループ", "bundled_assets_export_group_by_asset_type_description": "バンドルされたアセットは、他のファイルからのアセットと同じように扱われます。", "bundled_assets_export_group_by_bundle_name": "バンドル名でグループ", "bundled_assets_export_group_by_bundle_name_description": "バンドルされたアセットをアセットバンドル名でグループ化します。", "bundled_assets_export_title": "バンドルされたアセットのエクスポートモード", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "(実験的機能)自動検出", "c_sharp_langage_version_config_auto_safe": "(安定)自動検出", "c_sharp_langage_version_config_latest": "C# 最新バージョン", "c_sharp_language_version_config_description": "スクリプトをデコンパイルする際に使用するC#バージョン。", "check_log_for_more_details": "詳細はログを確認してください", "config_options": "起動構成", "config_screen_drag_drop_prompt": "ファイルまたはフォルダをドラッグ&ドロップしてください。\n左上のメニューから手動で開くこともできます。", "enable_prefab_outlining": "Prefab Outliningを有効にする", "enable_static_mesh_separation": "Static Mesh Separationを有効にする", "error": "エラー", "error_exporting_with_reason": "ゲームコンテンツのエクスポートに失敗しました: {0}", "error_importing_with_reason": "ゲームコンテンツの読み込みに失敗しました: {0}", "export_complete": "エクスポートが完了しました", "export_deleting_old_files": "既存のファイルを整理しています...", "export_in_progress": "アセットファイルをエクスポートしています\n{0}%\n{1}/{2}", "export_in_progress_no_file_count_yet": "アセットファイルをエクスポートしています\n0.0%\n?/?", "export_preparing": "エクスポート準備中...\nしばらくお待ち下さい。", "ignore_engine_assets": "Engine Assetsを無視する", "image_export_title": "Image出力形式", "image_format_description": "全てこの形式でエクスポートされます", "loading_game_content_from": "{0} からゲームコンテンツを読み込んでいます\n{1}", "loading_step_begin_scheme_processing": "スキーム処理をしています", "loading_step_create_file_collection": "ファイルコレクションを作成しています", "loading_step_detect_platform": "ファイルの収集とゲーム構成を検出しています", "loading_step_generate_dummy_dll": "IL2CPPからMonoアセンブリを生成しています", "loading_step_initialize_layout": "アセットレイアウトを初期化しています", "loading_step_load_assemblies": "アセンブリを読み込んでいます", "loading_step_load_assets_from_file": "{0} からアセットを読み込んでいます", "loading_step_locate_key_functions": "IL2CPPバイナリのライブラリ関数をスキャンしています", "loading_step_parse_archive": "アーカイブファイルを解析中 {0}", "loading_step_parse_bundle": "バンドルを解析中 {0}", "loading_step_parse_il2cpp_metadata": "IL2CPPメタデータを解析中", "loading_step_parse_resource": "リソースファイルを解析中 {0}", "loading_step_parse_serialized": "シリアライズファイルを解析中 {0}", "loading_step_parse_web": "Webファイルを解析中 {0}", "loading_step_pre_processing": "ファイルを前処理しています", "loading_step_restore_attributes": "生成されたアセンブリの属性を復元しています", "loose_file_saved_at": "ファイル群(Loose file)は {0} に保存されました", "menu_export": "エクスポート", "menu_export_all": "全てのファイル", "menu_export_selected": "選択中のファイル", "menu_export_selected_type": "選択した種類のファイル", "menu_file": "ファイル", "menu_file_exit": "終了", "menu_file_open_file": "ファイルを開く", "menu_file_open_folder": "フォルダを開く", "menu_file_reset": "閉じる", "menu_language": "言語", "mesh_export_title": "Mesh出力形式", "mesh_format_glb": "GLB", "mesh_format_glb_description": "FBXに代わるオープンソースフォーマットです。メッシュデータのみ出力します。(破損したファイルが出力される可能性があります)", "mesh_format_native": "Yaml", "mesh_format_native_description": "Unityエディタで使用できる形式です。Unityパッケージを使用して他のフォーマットへ変換できます。", "no_game_files_found": "ドロップされたファイルからアセットを発見できませんでした。", "not_implemented_yet": "未実装", "script_content_level_0": "レベル 0", "script_content_level_0_description": "スクリプトはエクスポートされません。", "script_content_level_1": "レベル 1", "script_content_level_1_description": "メソッドはエクスポートされません。", "script_content_level_2": "レベル 2", "script_content_level_2_description": "(デフォルト)Monoの場合は全メソッド、IL2Cppの場合はダミーメソッドがエクスポートされます。", "script_content_level_3": "レベル 3", "script_content_level_3_description": "(IL2Cpp)正常な場合に限りメソッドを回収します。", "script_content_level_4": "レベル 4", "script_content_level_4_description": "(IL2Cpp)正常性を無視してメソッドを回収します。", "script_content_level_title": "スクリプト出力レベル", "script_export_format_decompiled": "Decompiled", "script_export_format_decompiled_description": "C#スクリプトの生成にILSpyを使用します。信頼性は高いですが、完璧な状態ではない可能性があります。", "script_export_format_dll_with_renaming": "DLL(Renameingあり)", "script_export_format_dll_without_renaming": "DLL(Renamingなし)", "script_export_format_dll_without_renaming_description": "(実験的機能)DLL形式でエクスポートします。全く動作しない可能性があります。", "script_export_format_hybrid": "Hybrid", "script_export_title": "Script出力形式", "script_language_version_title": "C#バージョン", "select_asset_to_view_info": "アセット情報を表示するには、左側のリストを選択してください。", "shader_asset_export_title": "Shader出力形式", "shader_asset_format_decompile": "Decompile", "shader_asset_format_decompile_description": "(実験的機能)シェーダーをShaderLab形式にデコンパイルしてエクスポートします。高確率で破損したシェーダーを出力します。DX11のみをサポートしています。", "shader_asset_format_disassembly": "Disassembly", "shader_asset_format_disassembly_description": "(実験的機能)シェーダーをディスアセンブルした状態でエクスポートします。破損したシェーダーを出力する可能性があります。", "shader_asset_format_dummy": "ダミー", "shader_asset_format_dummy_description": "ダミーシェーダーとしてエクスポートします。", "shader_asset_format_yaml": "Yaml", "shader_asset_format_yaml_description": "(実験的機能)シェーダーをyamlとしてエクスポートします。Unity以外では使用できません。エディタがファイルを破損させる可能性があります。", "skip_streaming_assets": "StreamingAssetsフォルダーを除外", "sprite_export_title": "Sprite出力形式", "sprite_format_native": "Unity", "sprite_format_native_description": "Unityのスプライト形式でエクスポートします。Unity以外では使用できません。", "sprite_format_texture": "テクスチャ", "sprite_format_texture_description": "スプライトシート画像として出力します。", "sprite_format_yaml": "Yaml", "sprite_format_yaml_description": "Unityエディタで使用できる形式です。スプライトの全メタデータを正確に復元できる唯一のモードです。", "success": "成功", "terrain_export_title": "Terrain出力形式", "terrain_format_heatmap": "3D Mesh", "terrain_format_heatmap_description": "地形の高さをハイトマップとしてエクスポートします。", "terrain_format_mesh": "3D Mesh", "terrain_format_mesh_description": "GLB形式の3Dメッシュとしてエクスポートします。", "terrain_format_native": "Yaml", "terrain_format_native_description": "Unityのネイティブ形式でエクスポートします。", "text_asset_export_title": "TextAsset出力形式", "text_asset_format_binary": "バイナリ", "text_asset_format_binary_description": "バイナリ(.bytes)としてエクスポートします。", "text_asset_format_parse": "停止", "text_asset_format_parse_description": "パースした状態でエクスポートします。(パースした形式とは関係なく全てプレーンテキスト形式(.txt)としてエクスポートされます)", "text_asset_format_text": "プレーンテキスト", "text_asset_format_text_description": "プレーンテキスト(.txt)としてエクスポートします", "welcome_title": "AssetRipperへようこそ", "warning_this_directory_is_not_empty_all_content_will_be_deleted": "警告：このディレクトリは空ではありません、すべての内容が削除されます。", "lightmap_texture_format_description": "これは、出力された全てのライトマップテクスチャに影響します。", "menu_import": "輸入", "size": "大きさ", "asset_tab_information": "情報", "failed_files": "失敗したファイル", "settings_can_only_be_changed_before_loading_files": "設定はファイル読み込む前にのみ変更できます。", "asset_tab_model": "モデル", "experimental": "実験性", "format": "フォーマット", "frequency": "周波数", "game_object": "ゲーム対象", "licenses": "許可", "load": "読み込む", "no_files_loaded": "ファイルが読み込まれいません", "save": "保存", "remove": "削除する", "privacy": "プライバシー", "replace": "置き換える", "resources": "資源", "save_raw_data": "生データを保存する", "save_settings_to_disk": "設定をディスクに保存する", "scene": "場面", "script": "スクリプト", "settings": "設定", "select_folder": "フォルダーを選択", "select_file": "ファイルを選択", "target_version_for_version_changing": "変更する目標バージョン", "width": "広さ", "welcome": "ようこそ", "view_loaded_files": "読み込まれたファイルを表示", "vertex_count": "頂点数", "transform": "変換", "export_unity_project": "Unityプロジェクト出力", "menu_load": "読み込む", "menu_view": "ビュー", "export_primary_content": "主要内容出力"}