{"welcome_title": "AssetRipper에 오신 것을 환영합니다.", "image_export_title": "Image 추출 형식", "shader_asset_export_title": "Shader 추출 형식", "audio_format_force_wav": "WAV 확장자로 변환", "text_asset_export_title": "TextAsset 추출 형식", "script_content_level_title": "Script 추출 수준", "script_language_version_title": "C# 언어 버전", "skip_streaming_assets": "StreamingAssets 폴더를 제외", "config_options": "환경 설정", "mesh_export_title": "Mesh 추출 형식", "sprite_export_title": "Sprite 추출 형식", "audio_export_title": "오디오 추출 형식", "terrain_export_title": "Terrain 추출 형식", "audio_format_native": ".raw", "script_export_title": "Script 추출 형식", "mesh_format_glb": ".glb", "audio_format_yaml": ".yaml", "c_sharp_language_version_config_description": "스크립트를 디컴파일할 때 사용할 C# 언어 버전입니다.", "config_screen_drag_drop_prompt": "일부 설정 변경으로 인해 오류를 발생시키거나 막을 수 있습니다.\n준비가 되면 게임 파일/폴더를 이 창으로 끌어다 놓거나 왼쪽 상단에 있는 메뉴를 사용하여 수동으로 열 수 있습니다.", "export_primary_content": "주요 콘텐츠 추출하기", "export_unity_project": "유니티 프로젝트 추출하기", "lightmap_texture_export_title": "Lightmap 텍스처 추출 형식", "lightmap_texture_format_description": "이는 모든 추출된 Lightmap 텍스처에 영향을 미칩니다.", "loading_step_detect_platform": "파일 수집 및 게임 형식 감지", "loading_step_initialize_layout": "에셋 레이아웃 초기화", "loading_step_load_assets_from_file": "{0}에서 에셋 로딩중", "loading_step_locate_key_functions": "라이브러리 기능를 위한 IL2Cpp 바이너리 스캔", "loading_step_parse_archive": "아카이브 파일 파싱중 {0}", "loading_step_parse_bundle": "번들 파싱중 {0}", "loading_step_parse_il2cpp_metadata": "IL2Cpp 메타데이터 파싱중", "loading_step_parse_resource": "리소스 파일 파싱중 {0}", "loading_step_parse_serialized": "직렬 파일 파싱중 {0}", "loading_step_parse_web": "웹 파일 파싱중 {0}", "loading_step_pre_processing": "파일 사전 처리중", "loading_step_generate_dummy_dll": "IL2Cpp에서 Mono 어셈블리 생성", "assembly_name": "Assembly 이름", "c_sharp_langage_version_config_3": "C# 3", "an_error_occured_during_decompilation": "디컴파일 중에 오류가 발생했습니다.", "appreciation_message": "AssetRipper를 도와주셔서 감사합니다!", "asset_bundle_name": "에셋 번들 이름", "asset_ripper_free": "AssetRipper 무료", "asset_ripper_premium": "AssetRipper 프리미엄", "asset_tab_audio_duration_unknown": "<알 수 없음>", "asset_tab_audio_play": "재생", "asset_tab_dependencies": "종속성", "asset_tab_development": "개발", "asset_tab_font": "폰트", "asset_tab_hex": "헥스", "asset_tab_image": "이미지", "asset_tab_information": "정보", "assets": "에셋", "audio_clip": "오디오 클립", "audio_format_force_wav_description": "모든 오디오 파일을 WAV 파일로 변환합니다. 유니티에 넣을 경우 파일을 다시 압축하여 품질이 저하될 수 있으므로 권장되지 않습니다.", "audio_format_yaml_description": "yaml 에셋과 resS 파일로 내보냅니다. 이건 안전한 옵션이며 문제가 발생했을 때의 백업입니다.", "bundle": "번들", "bundled_assets_export_direct_export": "다이렉트 추출", "bundled_assets_export_group_by_bundle_name_description": "번들 에셋이 이름별로 그룹화됩니다.", "bundled_assets_export_direct_export_description": "번들 에셋이 그룹화 없이 추출됩니다.", "bundles": "번들", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "자동 - 실험기능", "c_sharp_langage_version_config_auto_safe": "자동 - 안전모드", "c_sharp_langage_version_config_latest": "C# 최신 버전", "channels": "채널", "class": "클래스", "class_id_type_name": "클래스 ID 형식 이름", "class_id_type_number": "클래스 ID 형식 숫자", "class_name": "클래스 이름", "collection": "컬렉션", "collections": "컬렉션들", "commands": "커맨드", "configuration_files": "구성 파일", "configuration_files_lists": "리스트", "check_log_for_more_details": "자세한 내용은 로그를 확인하세요", "configuration_files_singletons": "싱글톤", "count": "카운트", "csharp_type": "C# 형식", "data": "데이터", "height": "높이", "default_version": "기본 버전", "donation_message": "AssetRipper가 마음에 드신다면 기부도 할 수 있어요:", "enable_asset_deduplication": "중복된 에셋 삭제 활성화", "enable_prefab_outlining": "Prefab Outlining 활성화", "enable_static_mesh_separation": "Static Mesh 분리 활성화", "error": "오류", "error_exporting_with_reason": "게임 컨텐츠 추출 실패: {0}", "error_importing_with_reason": "게임 컨텐츠 로드 실패: {0}", "experimental": "실험", "export_complete": "추출 성공!", "export_deleting_old_files": "기존 파일 지우는 중...", "export_in_progress": "에셋 파일 추출중\n{0}%\n{1}/{2}", "export_in_progress_no_file_count_yet": "에셋 파일 추출중\n0.0%\n?/?", "export_preparing": "추출 준비중...\n1분 정도 소요됩니다.", "format": "포맷", "frequency": "빈도", "game_object": "게임 오브젝트", "guid": "GUID", "home": "홈", "image_format_description": "이는 모든 추출된 이미지에 영향을 미칩니다.", "json": "Json", "length": "길이", "licenses": "라이선스", "load": "로드", "loading_game_content_from": "{0}에서 게임 콘텐츠 로딩중\n{1}", "loading_step_begin_scheme_processing": "시작 프로세스 처리중", "loading_step_create_file_collection": "파일 컬렉션 생성", "loading_step_load_assemblies": "어셈블리 로딩중", "audio_format_default": "기본", "audio_format_native_description": "순수 FSB 오디오. 유니티에 넣을 수 없으므로 고급 사용자인 경우에만 사용하세요.", "c_sharp_langage_version_config_7_3": "C# 7.3", "bundled_assets_export_group_by_bundle_name": "번들 이름으로 그룹화", "bundled_assets_export_group_by_asset_type_description": "번들 에셋이 다른 파일의 에셋과 동일하게 취급됩니다.", "bundled_assets_export_title": "번들 에셋 추출 모드", "c_sharp_langage_version_config_2": "C# 2", "asset_tab_text": "텍스트", "bundled_assets_export_group_by_asset_type": "에셋 형식으로 그룹화", "asset_tab_audio_pause": "일시 정지", "asset_tab_audio": "오디오", "audio_format_default_description": "FSB 내에 포함된 콘텐츠 유형으로 에셋을 추출합니다. 대부분 오디오들은 WAV 형식으로 추출되고 일부는 OGG로 내보내집니다.", "loading_step_restore_attributes": "생성된 어셈블리 속성 복원"}