{"asset_tab_audio": "Dźwięk", "asset_tab_audio_duration_unknown": "<Nieznany>", "asset_tab_audio_pause": "<PERSON><PERSON>", "asset_tab_audio_play": "<PERSON><PERSON>", "asset_tab_basic": "Podstawowe Informacje", "asset_tab_image": "<PERSON><PERSON><PERSON>", "asset_tab_text": "Tekst", "asset_tab_yaml": "YAML", "audio_export_title": "Audio Export Format", "audio_format_default": "Domyślny", "audio_format_default_description": "Eksportuj zasoby jako typ zawartości osadzony w FSB. Większość typów audio jest eksportowana jako WAV, niektóre są eksportowane jako OGG.", "audio_format_force_wav": "Konwersja do formatu WAV", "audio_format_force_wav_description": "Konwersja wszystkich plików audio do plików WAV. Niezalecane w przypadku importowania do Unity, ponieważ może to spowodować ponowną kompresję plików, powodując utratę jakości.", "audio_format_native": "Raw", "audio_format_native_description": "Nieprzetworzony dźwięk FSB. Nie można zaimportować do Unity, więc używaj tego tylko, je<PERSON><PERSON> jeste<PERSON> z<PERSON>wansowanym użytkownikiem.", "audio_format_yaml": "YAML", "audio_format_yaml_description": "Eksport jako zasób yaml i plik resS. Jest to bezpieczna opcja i stanowi kopię zapasową, gdy coś pójdzie nie tak.", "bundled_assets_export_direct_export": "Bezpośredni Export", "bundled_assets_export_direct_export_description": "Zasoby w pakiecie są eksportowane bez grupowania.", "bundled_assets_export_group_by_asset_type": "Grupuj według Typu Zasobów", "bundled_assets_export_group_by_asset_type_description": "Zasoby w pakiecie są traktowane tak samo jak zasoby z innych plików.", "bundled_assets_export_group_by_bundle_name": "Grupuj według nazwy pakietu", "bundled_assets_export_group_by_bundle_name_description": "Zasoby w pakietach są grupowane według nazwy pakietu zasobów.", "bundled_assets_export_title": "Bundled Assets Export Mode", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "Automatyczne - Eksperymentalne", "c_sharp_langage_version_config_auto_safe": "Automatyczne - Bezpieczne", "c_sharp_langage_version_config_latest": "Najnowszy C#", "c_sharp_language_version_config_description": "Wersja języka C# używana podczas dekompilacji skryptów.", "check_log_for_more_details": "Więcej szczegółów w dzienniku", "config_options": "<PERSON><PERSON><PERSON>", "config_screen_drag_drop_prompt": "<PERSON><PERSON><PERSON><PERSON>, że niektóre zmiany ustawień mogą powodować lub zapobiegać błędom.\n<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> gotowy, przeciągnij i upuść plik/folder gry do tego okna lub użyj menu w lewym górnym rogu, aby otwo<PERSON><PERSON>ć coś ręcznie.", "enable_prefab_outlining": "Włącz Prefab Outlining", "enable_static_mesh_separation": "Włącz Static Mesh Separation", "error": "Błąd", "error_exporting_with_reason": "<PERSON>e udało się wyeksportować zawartości gry: {0}", "error_importing_with_reason": "<PERSON>e udało się załadować zawartości gry: {0}", "export_complete": "Eksport został zakończony!", "export_deleting_old_files": "Czyszczenie istniejących plików...", "export_in_progress": "Eksportowanie plików zasobów\n{0}%\n{1}/{2}", "export_in_progress_no_file_count_yet": "Eksportowanie plików zasobów\n0.0%\n?/?", "export_preparing": "Przygotowanie do eksportu...\nMoże to zająć chwilę.", "ignore_engine_assets": "Zignoruj Engine Assets", "image_export_title": "Image Export Format", "image_format_description": "Wpływa na wszystkie eksportowane obrazy", "loading_game_content_from": "Ładowanie zawartości gry od {0}\n{1}", "loading_step_begin_scheme_processing": "Rozpoczęcie przetwarzania schematu", "loading_step_create_file_collection": "Tworzenie kolekcji plików", "loading_step_detect_platform": "Zbieranie plików i wykrywanie struktury gry", "loading_step_generate_dummy_dll": "Generowanie złożeń Mono z IL2CPP", "loading_step_initialize_layout": "Inicjowanie układu z<PERSON>obów", "loading_step_load_assemblies": "Ładowanie podzespołów", "loading_step_load_assets_from_file": "Ładowanie zasobów z {0}", "loading_step_locate_key_functions": "Skanowanie pliku binarnego IL2CPP w poszukiwaniu funkcji bibliotecznych", "loading_step_parse_archive": "Analizowanie pliku archiwum {0}", "loading_step_parse_bundle": "<PERSON><PERSON>zo<PERSON><PERSON> paki<PERSON>u {0}", "loading_step_parse_il2cpp_metadata": "Analiza metadanych IL2CPP", "loading_step_parse_resource": "Analizowanie pliku zasobów {0}", "loading_step_parse_serialized": "Analizowanie pliku serializowanego {0}", "loading_step_parse_web": "Analizowanie pliku sieci Web {0}", "loading_step_pre_processing": "Pliki wstępnego przetwarzania", "loading_step_restore_attributes": "Przywracanie atrybutów wygenerowanych złożeń", "loose_file_saved_at": "Luźny plik zapisany pod adresem {0}", "menu_export": "Eksport", "menu_export_all": "Eksport wszystkich plików", "menu_export_selected": "Eksportuj wybrane pliki", "menu_export_selected_type": "Eksportuj wszystkie pliki danego typu", "menu_file": "Plik", "menu_file_exit": "Wyjdź", "menu_file_open_file": "Otwórz <PERSON>", "menu_file_open_folder": "Otwórz Folder", "menu_file_reset": "<PERSON><PERSON><PERSON><PERSON>", "menu_language": "Język", "mesh_export_title": "Mesh Export Format", "mesh_format_glb": "GLB", "mesh_format_glb_description": "<PERSON><PERSON><PERSON><PERSON>, open-source'owa alternatywa dla FBX. Binarna wersja GLTF. Zawiera tylko dane siatki. Może powodować błędy. Unity nie może importować zasobów tego typu.", "mesh_format_native": "YAML", "mesh_format_native_description": "Stabilny format do używania siatek w edytorze. Może być konwertowany do innych formatów przez różne pakiety Unity.", "no_game_files_found": "W upuszczonych plikach nie znaleziono żadnej gry Unity ani pakietu zasobów.", "not_implemented_yet": "Jeszcze nie zostało wdrożone", "script_content_level_0": "Poziom 0", "script_content_level_0_description": "Skrypty nie są eksportowane.", "script_content_level_1": "Poziom 1", "script_content_level_1_description": "<PERSON><PERSON> s<PERSON> usuwane z dekompilowanego eksportu.", "script_content_level_2": "Poziom 2", "script_content_level_2_description": "Domyślnie. Eksportuje pełne metody dla gier Mono i dummy methods dla gier IL2Cpp.", "script_content_level_3": "Poziom 3", "script_content_level_3_description": "Metody IL2Cpp są bezpiecznie odzyskiwane tam, gdzie to możliwe.", "script_content_level_4": "Poziom 4", "script_content_level_4_description": "Metody IL2Cpp są odzyskiwane bez względu na bezpieczeństwo.", "script_content_level_title": "Script Content Level", "script_export_format_decompiled": "Decompiled", "script_export_format_decompiled_description": "Dekompilator ILSpy jest używany do generowania skryptów CS. Jest on niezawodny. Jest jednak czasochłonny i zawiera wiele błędów kompilacji.", "script_export_format_dll_with_renaming": "Dll Export ze zmianą nazwy", "script_export_format_dll_without_renaming": "Dll Export bez zmiany nazwy", "script_export_format_dll_without_renaming_description": "Zespoły są eksportowane w skompilowanej formie Dll. Eksperymentalne. Może w ogóle nie d<PERSON>.", "script_export_format_hybrid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "script_export_title": "Shader Export Format", "script_language_version_title": "Wersja języka C#", "select_asset_to_view_info": "<PERSON><PERSON><PERSON><PERSON> po lewej stronie, aby wy<PERSON><PERSON><PERSON>lić informacje na jego temat", "shader_asset_export_title": "Shader Export Format", "shader_asset_format_decompile": "Decompile", "shader_asset_format_decompile_description": "Eksport shadera do ShaderLab. Bardzo eksperymentalny i prawie na pewno będzie zawierał błędy. Obsługuje tylko DX11, nie DX9.", "shader_asset_format_disassembly": "Disassembly", "shader_asset_format_disassembly_description": "Eksport shadera jako disassembly. Eksperymentalne i podatne na uszkodzenia. Nie kompiluje się w edytorze.", "shader_asset_format_dummy": "Dummy <PERSON>", "shader_asset_format_dummy_description": "Eksportuj shader jako Dummy Shader. <PERSON><PERSON><PERSON> zachowuje on dane takie jak Właściwości i FallBack, używa ogólnego, nieprzezroczystego kodu shadera.", "shader_asset_format_yaml": "<PERSON><PERSON><PERSON><PERSON>", "shader_asset_format_yaml_description": "Eksportuje shader jako yaml asset. Eksperymentalnie i nadaje się tylko do przeglądania w edytorze. Edytor może losowo uszkodzić te pliki.", "skip_streaming_assets": "Pomiń Folder StreamingAssets", "sprite_export_title": "Image Export Format", "sprite_format_native": "Unity", "sprite_format_native_description": "Eksport w Unity Sprite Format. Nie może być wyświetlany poza unity.", "sprite_format_texture": "Tekstury", "sprite_format_texture_description": "Eksport jako Sprite Sheet. <PERSON><PERSON><PERSON>lądany poza Unity, ale wolniej eksportowany.", "sprite_format_yaml": "YAML", "sprite_format_yaml_description": "Eksport jako z<PERSON>oby ya<PERSON>l, które można przeglądać w edytorze. Jest to j<PERSON><PERSON><PERSON> tryb, kt<PERSON>ry zapewnia dokładne odzyskanie wszystkich metadanych sprite'ów.", "success": "Sukces!", "terrain_export_title": "Image Export Format", "terrain_format_heatmap": "Heightmap", "terrain_format_heatmap_description": "Eksport mapy cieplnej wysokości terenu w każdej lokalizacji. Przydatne tylko wtedy, gdy nie zależy ci na szczegółach lub posiadaniu terenu w 3D.", "terrain_format_mesh": "3D Mesh", "terrain_format_mesh_description": "Eksportuj teren jako siatkę 3D w formacie GLB, odpowiednią do wyświetlania w szerokiej gamie edytorów 3D.", "terrain_format_native": "YAML", "terrain_format_native_description": "Eksport w Native Unity Terrain Format. Najbardziej przydatna opcja, je<PERSON><PERSON> planujesz ponowny import do unity.", "text_asset_export_title": "Image Export Format", "text_asset_format_binary": "Bajtów", "text_asset_format_binary_description": "Eksportuj bajty Text Asset z rozszerzeniem .bytes.", "text_asset_format_parse": "Analizuj", "text_asset_format_parse_description": "Eksportuj jako zwykły plik tekstowy, ale spróbuj odgadnąć prawidłowe rozszerzenie pliku (np. pliki JSON otrzymują rozszerzenie .json).", "text_asset_format_text": "Czysty tekst", "text_asset_format_text_description": "Eksport jako zwykły plik tekstowy (.txt)", "welcome_title": "Witaj w AssetRipper"}