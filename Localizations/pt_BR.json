{"an_error_occured_during_decompilation": "Um erro ocorreu durante a descompilação.", "assembly_name": "Nome do Assembly", "asset_bundle_name": "Nome do Pacote de Asset", "asset_ripper_free": "AssetRip<PERSON>", "asset_ripper_premium": "AssetRipper Premium", "asset_tab_audio": "<PERSON><PERSON><PERSON>", "asset_tab_audio_duration_unknown": "<Desconhecido>", "asset_tab_audio_pause": "Pausar", "asset_tab_audio_play": "Reproduzir", "asset_tab_basic": "Informações Básicas", "asset_tab_dependencies": "Dependências", "asset_tab_development": "Desenvolvimento", "asset_tab_font": "Fonte", "asset_tab_hex": "Hex", "asset_tab_image": "Imagem", "asset_tab_information": "Informação", "asset_tab_text": "Texto", "asset_tab_yaml": "Yaml", "assets": "Assets", "audio_clip": "AudioClip", "audio_export_title": "Formato de Exportação de Áudio", "audio_format_default": "Padrão", "audio_format_default_description": "Exporta assets como o tipo de conteúdo embutido dentro do FSB. A maioria dos tipos de áudio são exportados como WAV, alguns como OGG.", "audio_format_force_wav": "Converter para WAV", "audio_format_force_wav_description": "Converte todos os arquivos de áudio para arquivos WAV. Não recomendado ao importar na Unity, pois pode recomprimir arquivos, causando perda de qualidade.", "audio_format_native": "Bru<PERSON>", "audio_format_native_description": "Áudio FSB bruto. Não pode ser importado na Unity, então use apenas se você for um usuário avançado.", "audio_format_yaml": "Yaml", "audio_format_yaml_description": "Exporta como um asset yaml e arquivo resS. Esta é uma opção segura e serve como backup quando as coisas dão errado.", "bundle": "<PERSON><PERSON>", "bundled_assets_export_direct_export": "Exportação Direta", "bundled_assets_export_direct_export_description": "Assets empacotados são exportados sem agrupamento.", "bundled_assets_export_group_by_asset_type": "Agrupar por Tipo de Asset", "bundled_assets_export_group_by_asset_type_description": "Assets empacotados são tratados da mesma forma que assets de outros arquivos.", "bundled_assets_export_group_by_bundle_name": "Agrupar por Nome do Pacote", "bundled_assets_export_group_by_bundle_name_description": "Assets empacotados são agrupados pelo nome de seu pacote de assets.", "bundled_assets_export_title": "Modo de Exportação de Assets Empacotados", "bundles": "<PERSON><PERSON>", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "Automático - Experimental", "c_sharp_langage_version_config_auto_safe": "Automático - Seguro", "c_sharp_langage_version_config_latest": "C# <PERSON><PERSON>", "c_sharp_language_version_config_description": "A versão da linguagem C# que será utilizada ao descompilar scripts.", "channels": "Canais", "check_log_for_more_details": "Verifique o log para mais detalhes", "class": "Classe", "class_id_type_name": "Nome do Tipo de ID de Classe", "class_id_type_number": "Número do Tipo de ID de Classe", "class_name": "Nome da Classe", "collection": "Coleção", "collections": "Coleções", "commands": "<PERSON><PERSON><PERSON>", "config_options": "Opções de Configuração", "config_screen_drag_drop_prompt": "Observe que algumas alterações nas configurações podem causar ou prevenir erros.\nQuando estiver pronto, arraste e solte o arquivo/pasta do seu jogo nesta janela, ou use o menu no canto superior esquerdo para abrir manualmente.", "count": "Contagem", "csharp_type": "Tipo C#", "data": "<PERSON><PERSON>", "default_version": "<PERSON><PERSON><PERSON>", "donation_message": "Se você gosta do AssetRipper, considere fazer uma doação:", "enable_prefab_outlining": "Habilitar Contorno de Prefab", "enable_static_mesh_separation": "Habilitar Separação de Malha Estática", "error": "Erro", "error_exporting_with_reason": "Falha ao exportar conteúdo do jogo: {0}", "error_importing_with_reason": "Falha ao carregar conteúdo do jogo: {0}", "export_complete": "Exportação Concluída!", "export_deleting_old_files": "Excluindo arquivos existentes...", "export_in_progress": "Exportando Arquivos de Assets\n{0}%\n{1}/{2}", "export_in_progress_no_file_count_yet": "Exportando Arquivos de Assets\n0.0%\n?/?", "export_preparing": "Preparando para Exportar...\n<PERSON><PERSON> pode levar um minuto.", "format": "Formato", "frequency": "Frequência", "game_object": "GameObject", "guid": "GUID", "height": "Altura", "home": "Início", "ignore_engine_assets": "<PERSON><PERSON><PERSON>", "image_export_title": "Formato de Exportação de Imagem", "image_format_description": "<PERSON><PERSON> a<PERSON>ta todas as imagens exportadas.", "json": "Json", "length": "Comprimento", "licenses": "Licenças", "loading_game_content_from": "Carregando Conteúdo do Jogo de {0}\n{1}", "loading_step_begin_scheme_processing": "Iniciando Processamento do Esquema", "loading_step_create_file_collection": "Criando Cole<PERSON> de Arquivos", "loading_step_detect_platform": "Coletando Arquivos e Detectando Estrutura do Jogo", "loading_step_generate_dummy_dll": "Gerando Assemblies Mono a Partir do IL2Cpp", "loading_step_initialize_layout": "Inicializando Layout do Asset", "loading_step_load_assemblies": "Carregando Assemblies", "loading_step_load_assets_from_file": "Carregando Assets de {0}", "loading_step_locate_key_functions": "Escaneando Binário IL2Cpp para Funções de Biblioteca", "loading_step_parse_archive": "Analisand<PERSON> Arqui<PERSON> {0}", "loading_step_parse_bundle": "<PERSON><PERSON><PERSON><PERSON> {0}", "loading_step_parse_il2cpp_metadata": "Analisando Metadados IL2Cpp", "loading_step_parse_resource": "Analisando Arquivo de Recursos {0}", "loading_step_parse_serialized": "Analisando Arquivo Serializado {0}", "loading_step_parse_web": "Analisando Arquivo Web  File {0}", "loading_step_pre_processing": "Pré-process<PERSON><PERSON>", "loading_step_restore_attributes": "Restaurando Atributos em Assemblies Gerados", "loose_file_saved_at": "Arquivo solto salvo em {0}", "main_asset": "Asset Principal", "menu_export": "Exportar", "menu_export_all": "Exportar Todos os Arquivos", "menu_export_selected": "Exportar Arquivo Selecionado", "menu_export_selected_type": "Exportar Todos os Arquivos do Tipo Selecionado", "menu_file": "Arquivo", "menu_file_exit": "<PERSON><PERSON>", "menu_file_open_file": "Abrir Arquivo", "menu_file_open_folder": "<PERSON><PERSON><PERSON>", "menu_file_reset": "Redefinir", "menu_language": "Idioma", "menu_load": "<PERSON><PERSON><PERSON>", "mesh": "<PERSON><PERSON>", "mesh_export_title": "Formato de Exportação de Malha", "mesh_format_glb": "GLB", "mesh_format_glb_description": "Uma alternativa de alta qualidade e de código aberto ao FBX. Versão binária do GLTF. Contém apenas dados de malha. Pode causar erros. Unity não pode importar assets deste tipo.", "mesh_format_native": "Yaml", "mesh_format_native_description": "Um formato robusto para usar malhas no editor. Pode ser convertido para outros formatos por uma variedade de pacotes da Unity.", "name": "Nome", "namespace": "Namespace", "no_files_loaded": "Nenhum Arquivo Carregado", "no_game_files_found": "Nenhum jogo <PERSON> ou pacote de asset foi encontrado nos arquivos fornecidos.", "not_implemented_yet": "Ainda Não Implementado", "original_path": "Caminho Original", "parent": "<PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "path_id": "ID do Caminho", "privacy": "Privacidade", "resources": "Recursos", "save": "<PERSON><PERSON>", "scene": "<PERSON><PERSON>", "script": "<PERSON><PERSON><PERSON>", "script_content_level_0": "Nível 0", "script_content_level_0_description": "Scripts não são exportados.", "script_content_level_1": "Nível 1", "script_content_level_1_description": "Métodos são removidos da exportação descompilada.", "script_content_level_2": "Nível 2", "script_content_level_2_description": "Padrão. Exporta métodos completos para jogos Mono e métodos fictícios para jogos IL2Cpp.", "script_content_level_3": "Nível 3", "script_content_level_3_description": "Métodos IL2Cpp são recuperados com segurança sempre que possível.", "script_content_level_4": "Nível 4", "script_content_level_4_description": "Métodos IL2Cpp são recuperados sem consideração à segurança.", "script_content_level_title": "Nível de Conteúdo do Script", "script_export_format_decompiled": "Descompilação", "script_export_format_decompiled_description": "O descompilador ILSpy é usado para gerar scripts CS. É confiável. No entanto, também é demorado e contém muitos erros de compilação.", "script_export_format_dll_with_renaming": "Exportar DLL com Renomeação", "script_export_format_dll_without_renaming": "Exportar DLL sem Renomeação", "script_export_format_dll_without_renaming_description": "Assemblies são exportados em sua forma compilada de DLL. A Unity provavelmente substituirá assemblies especiais como Assembly-CSharp.", "script_export_format_hybrid": "<PERSON><PERSON><PERSON><PERSON>", "script_export_format_hybrid_description": "Assemblies especiais como Assembly-CSharp são descompilados. Outros assemblies são exportados em sua forma compilada de DLL.", "script_export_title": "Formato de Exportação de Script", "script_language_version_title": "Versão da Linguagem C#", "select_asset_to_view_info": "Selecione um asset à esquerda para ver informações sobre ele", "settings": "Configurações", "settings_can_only_be_changed_before_loading_files": "As configurações só podem ser alteradas antes de carregar arquivos.", "shader": "Shader", "shader_asset_export_title": "Formato de Exportação de Shader", "shader_asset_format_decompile": "Descompilação", "shader_asset_format_decompile_description": "Exporta o shader para ShaderLab. Muito experimental e quase certamente terá erros. Suporta apenas DX11, não DX9.", "shader_asset_format_disassembly": "Desmontagem", "shader_asset_format_disassembly_description": "Exporta o shader como desmontagem. Experimental e suscetível a falhas. Não compilará no editor.", "shader_asset_format_dummy": "<PERSON><PERSON>", "shader_asset_format_dummy_description": "Exporta o shader como um shader fictício. Embora preserve dados como propriedades e fallback, ele utiliza código de shader geral e opaco.", "shader_asset_format_yaml": "<PERSON><PERSON>", "shader_asset_format_yaml_description": "Exporta o shader como um asset yaml. Experimental e apenas utilizável para visualização no editor. O editor pode corromper aleatoriamente esses arquivos.", "skip_streaming_assets": "Pular Pasta StreamingAssets", "sprite_export_title": "Formato de Exportação de Sprite", "sprite_format_native": "Unity", "sprite_format_native_description": "Exporta no formato de sprite da Unity. Não pode ser visualizado fora da Unity.", "sprite_format_texture": "Textura", "sprite_format_texture_description": "Exporta como uma imagem da folha de sprites. Pode ser visualizado fora do Unity, mas é mais lento para exportar.", "sprite_format_yaml": "Yaml", "sprite_format_yaml_description": "Exporta como assets yaml que podem ser visualizados no editor. Este é o único modo que garante uma recuperação precisa de todos os metadados dos sprites.", "submesh_count": "Contagem de Submalha", "success": "Sucesso!", "terrain_export_title": "Formato de Exportação de Terreno", "terrain_format_heatmap": "Mapa de Altura", "terrain_format_heatmap_description": "Exporta um mapa de calor da altura do terreno em cada localização. Apenas realmente útil se você não se importar com os detalhes ou de ter o terreno em 3D.", "terrain_format_mesh": "Malha 3D", "terrain_format_mesh_description": "Exporta o terreno como uma malha 3D no formato GLB, adequado para visualização com uma ampla variedade de editores 3D.", "terrain_format_native": "Yaml", "terrain_format_native_description": "Exporta no formato nativo de terreno da Unity. Opção mais útil se planeja reimportar de volta para a Unity.", "text_asset_export_title": "Formato de Exportação de TextAsset", "text_asset_format_binary": "Bytes", "text_asset_format_binary_description": "Exporta os dados brutos do asset de texto como um arquivo BYTES.", "text_asset_format_parse": "<PERSON><PERSON><PERSON>", "text_asset_format_parse_description": "Exporta como um arquivo de texto simples, mas tenta adivinhar a extensão de arquivo correta (por exemplo, arquivos JSON recebem a extensão .json).", "text_asset_format_text": "Texto Simples", "text_asset_format_text_description": "Exporta como um arquivo de texto simples (.txt).", "texture": "Textura", "transform": "Transformação", "vertex_count": "Contagem de Vértices", "view_loaded_files": "Ver Arquivos Carregados", "welcome": "<PERSON><PERSON>-vindo", "welcome_title": "Bem-vindo ao AssetRipper", "width": "<PERSON><PERSON><PERSON>", "yaml": "Yaml"}