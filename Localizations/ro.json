{"asset_tab_audio": "Audio", "asset_tab_audio_duration_unknown": "<Necunoscut>", "lightmap_texture_export_title": "Format de export textură lightmap", "lightmap_texture_format_description": "Aceasta afectează toate texturile lightmap exportate.", "loading_step_create_file_collection": "<PERSON><PERSON><PERSON> co<PERSON>ț<PERSON> de fișiere", "loading_step_initialize_layout": "Inițializare aspect resurse", "loading_step_load_assets_from_file": "<PERSON><PERSON><PERSON><PERSON><PERSON> resurse din {0}", "loading_step_parse_archive": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>hi<PERSON> {0}", "loading_step_parse_bundle": "<PERSON><PERSON><PERSON><PERSON> {0}", "loading_step_parse_il2cpp_metadata": "Analizare metadata IL2Cpp", "loading_step_parse_resource": "<PERSON><PERSON><PERSON><PERSON> resurse {0}", "loading_step_parse_serialized": "<PERSON><PERSON><PERSON><PERSON> serializat {0}", "loading_step_parse_web": "<PERSON><PERSON><PERSON><PERSON> {0}", "loading_step_pre_processing": "Preprocesare fișiere", "main_asset": "Resursa principală", "menu_export": "Exportă", "menu_export_all": "Exportă toate <PERSON><PERSON><PERSON><PERSON>", "menu_export_selected": "Exportă fișierele selectate", "menu_file": "Fișiere", "menu_file_exit": "Ieșire", "menu_file_open_file": "<PERSON><PERSON><PERSON> fi<PERSON>ul", "menu_file_open_folder": "Deschide folderul", "menu_file_reset": "Resetează", "menu_import": "Importă", "menu_load": "Încar<PERSON><PERSON>", "menu_view": "Vizualizare", "mesh": "<PERSON><PERSON>", "mesh_export_title": "Format de Exportare Mesh", "mesh_format_glb": "GLB", "mesh_format_native": "Yaml", "mesh_format_native_description": "Un format robust pentru utilizarea mesh-urilor în editor. Poate fi convertit în alte formate printr-o varietate de pachete Unity.", "name": "Nume", "no_data_has_been_loaded_for_this_key": "Nu s-au încărcat date pentru această cheie.", "no_files_loaded": "<PERSON><PERSON><PERSON>", "not_implemented_yet": "Nu este încă implementat", "original_path": "Calea originală", "parent": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON>", "path_id": "ID Cale", "privacy": "Confidențialitate", "remove": "Elimină", "replace": "Înlocuieș<PERSON>", "resources": "Resurse", "loading_step_load_assemblies": "Încărcare Assemblies", "loading_step_restore_attributes": "Restaurare atributelor Assembli generate", "namespace": "Spațiu de nume", "save_raw_data": "Salvează date brute", "save_settings_to_disk": "Salvează setările pe disc", "scene": "Scenă", "script": "<PERSON><PERSON><PERSON>", "script_content_level_0": "Nivel 0", "script_content_level_1": "Nivel 1", "script_content_level_1_description": "Metodele sunt eliminate din exportul decompilat.", "script_content_level_2": "Nivel 2", "script_content_level_3": "Nivel 3", "script_content_level_3_description": "Metodele IL2Cpp sunt recuperate în siguranță acolo unde este posibil.", "script_content_level_4": "Nivel 4", "script_content_level_4_description": "Metodele IL2Cpp sunt recuperate fără a ține cont de siguranță.", "script_content_level_title": "Nivel de Conținut al Scriptului", "script_export_format_decompiled": "Decompilare", "script_export_format_dll_with_renaming": "Export DLL cu redenumire", "script_export_format_dll_without_renaming": "Export DLL fără redenumire", "script_export_format_hybrid": "<PERSON><PERSON>d", "script_export_title": "Format de Export al Scriptului", "script_language_version_title": "Versiune Limbaj C#", "select_file": "<PERSON><PERSON><PERSON><PERSON>", "select_folder": "Selectează folder", "settings": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON><PERSON>", "sprite_format_texture": "Textură", "submesh_count": "<PERSON><PERSON><PERSON><PERSON>", "success": "Succes!", "target_version_for_version_changing": "Versiune Țintă pentru Schimbarea Versiunii", "terrain_export_title": "Format de Export Terrain", "terrain_format_heatmap": "Heightmap", "terrain_format_heatmap_description": "Exportă un heatmap al înălțimii terenului la fiecare locație. Util numai dacă nu îți pasă de detalii sau de a avea terenul în 3D.", "terrain_format_mesh": "Mesh 3D", "terrain_format_mesh_description": "Exportă terenul ca un mesh 3D în format GLB, potrivit pentru vizualizare cu o gamă largă de editoare 3D.", "text_asset_export_title": "Format de Export TextAsset", "script_export_format_hybrid_description": "Asamblările speciale, cum ar fi Assembly-CSharp, sunt decompilate. Alte asamblări sunt exportate în forma lor compilată DLL.", "text_asset_format_binary": "Bytes", "text_asset_format_binary_description": "Exportă datele brute ale asset-ului de text ca fișier BYTES.", "text_asset_format_parse_description": "Exportă ca fișier text simplu, dar încearcă să ghicească extensia corectă a fișierului (de exemplu, fișierele JSON primesc extensia .json).", "text_asset_format_text": "Text Simplu", "text_asset_format_text_description": "Exportă ca fișier text simplu (.txt).", "texture": "Textură", "there_are_no_keys_for_this_data_type": "Nu există chei pentru acest tip de date.", "vertex_count": "<PERSON><PERSON><PERSON><PERSON>", "warning_this_directory_is_not_empty_all_content_will_be_deleted": "Atenție: acest director nu este gol. Tot conținutul va fi șters.", "welcome": "Salut", "yaml": "Yaml", "text_asset_format_parse": "<PERSON><PERSON><PERSON>", "transform": "Transform", "sprite_format_native": "Unity", "sprite_format_native_description": "Exportă în formatul sprite Unity. Nu poate fi vizualizat în afara Unity.", "settings_can_only_be_changed_before_loading_files": "Setările pot fi schimbate doar înainte de încărcarea fișierelor.", "shader": "Shader", "shader_asset_export_title": "Format de Export Shader", "shader_asset_format_decompile": "Decompilare", "shader_asset_format_decompile_description": "Exportă shader-ul în ShaderLab. Foarte experimental și aproape cu siguranță va avea erori. Suportă doar DX11, nu DX9.", "shader_asset_format_disassembly_description": "Exportă shader-ul ca disassembly. Experimental și predispus la defecțiuni. Acesta nu se compilează în editor.", "shader_asset_format_disassembly": "Disassembly", "shader_asset_format_dummy": "Shader Fals", "shader_asset_format_dummy_description": "Exportă shader-ul ca shader fals. Deși păstrează date precum proprietățile și fallback-ul, folosește cod shader general și opac.", "shader_asset_format_yaml": "<PERSON><PERSON>", "shader_asset_format_yaml_description": "Exportă shader-ul ca asset yaml. Experimental și utilizabil doar pentru vizualizare în editor. Editorul poate corupe aleatoriu aceste fișiere.", "skip_streaming_assets": "Omitere folder StreamingAssets", "sprite_export_title": "Format de Export Sprite", "terrain_format_native": "Yaml", "terrain_format_native_description": "\"Exportă în formatul nativ Unity pentru teren. Opțiunea cea mai utilă dacă intenționezi să reimporti în Unity.", "asset_bundle_name": "Numele pachetului de resurse", "asset_tab_development": "Dezvoltare", "assembly_name": "Numele Assembly", "bundle": "<PERSON><PERSON><PERSON>", "bundled_assets_export_direct_export": "Export direct", "bundled_assets_export_direct_export_description": "Resursele grupate sunt exportate fără a fi grupate.", "bundled_assets_export_group_by_asset_type": "Grupare după tipul resursei", "bundles": "Pachete", "appreciation_message": "Îți mulțumim pentru că susții AssetRipper!", "asset_ripper_free": "AssetRipper Gratuit", "asset_ripper_premium": "AssetRipper Premium", "asset_tab_audio_pause": "Pauză", "asset_tab_audio_play": "Redare", "asset_tab_dependencies": "Dependențe", "asset_tab_font": "Font", "asset_tab_hex": "Hex", "asset_tab_image": "Imagine", "asset_tab_information": "Informație", "asset_tab_text": "Text", "assets": "Resurse", "audio_clip": "Clip Audio", "audio_export_title": "Format de export audio", "audio_format_default": "Normal", "audio_format_force_wav": "Conversie în WAV", "audio_format_force_wav_description": "Convertește toate fișierele audio în fișiere WAV. Nu este recomandat pentru import în Unity, deoarece poate recomprima fișierele, cauz<PERSON>d pierderi de calitate.", "audio_format_native": "B<PERSON><PERSON>", "audio_format_yaml": "Yaml", "audio_format_yaml_description": "Exportă ca resursă yaml și fișier resS. Aceasta este o opțiune sigură și este soluția de rezervă când lucrurile merg prost.", "bundled_assets_export_group_by_asset_type_description": "Resursele grupate sunt tratate la fel ca resursele din alte fișiere.", "bundled_assets_export_group_by_bundle_name": "Grupare după numele pachetului", "bundled_assets_export_title": "Mod de export al resurselor grupate", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "Automat - Experimental", "c_sharp_langage_version_config_auto_safe": "Automat - Sigur", "c_sharp_langage_version_config_latest": "C# Ultima versiune", "channels": "Canale", "check_log_for_more_details": "Verifică jurnalul pentru mai multe detalii", "class_id_type_number": "<PERSON>um<PERSON>r T<PERSON>lasă", "class_name": "<PERSON>ume <PERSON>", "collection": "<PERSON><PERSON><PERSON><PERSON>", "collections": "Colecții", "commands": "<PERSON><PERSON><PERSON>", "config_options": "Opțiuni de configurare", "configuration_files": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "configuration_files_lists": "Liste", "count": "<PERSON><PERSON><PERSON><PERSON>", "csharp_type": "Tip C#", "data": "Date", "default_version": "Versiune implicită", "donation_message": "Dacă îți place <PERSON><PERSON><PERSON><PERSON><PERSON>, te rugăm să iei în considerare o donație:", "enable_asset_deduplication": "Activează deduplicarea resurselor", "enable_prefab_outlining": "Activează conturarea prefaburilor", "error": "Eroare", "error_exporting_with_reason": "Nu s-a reușit exportul conținutului jocului: {0}", "error_importing_with_reason": "Nu s-a reușit încărcarea conținutului jocului: {0}", "experimental": "Experimental", "export_complete": "Export Complet!", "export_in_progress": "Exportare Fișiere Resurse\n{0}%\n{1}/{2}", "export_in_progress_no_file_count_yet": "Exportare Fișiere Resurse\n0.0%\n?/?", "export_preparing": "Pregătire pentru export...\nAcest lucru poate dura un minut.", "format": "Format", "frequency": "<PERSON>ec<PERSON><PERSON>", "game_object": "GameObject", "guid": "GUID", "height": "Înălțime", "home": "Acasă", "image_export_title": "Format de export imagine", "image_format_description": "Aceasta afectează toate imaginile exportate.", "json": "Json", "length": "Lungime", "licenses": "Licențe", "load": "Încar<PERSON><PERSON>", "loading_game_content_from": "Înc<PERSON><PERSON><PERSON> conținut joc din {0}\n{1}", "an_error_occured_during_decompilation": "A apărut o eroare în timpul decompilării.", "class": "Clasă", "class_id_type_name": "Nume Tip ID Clasă", "audio_format_default_description": "Exportă resursele ca tipul de conținut încorporat în FSB. Majoritatea tipurilor de audio sunt exportate ca WAV, unele sunt exportate ca OGG.", "audio_format_native_description": "Audio FSB brut. Nu poate fi importat în Unity, deci folosiți doar dacă sunteți un utilizator avansat.", "width": "Lățime", "bundled_assets_export_group_by_bundle_name_description": "Resursele grupate sunt grupate după numele pachetului de resurse.", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_language_version_config_description": "Versiunea limbajului C# care va fi utilizată la decompilarea scripturilor.", "config_screen_drag_drop_prompt": "Rețineți că unele modificări ale setărilor pot cauza sau preveni erori.\nOdată ce sunteți gata, trageți și eliberați fișierul/jocul dvs. în această fereastră sau folosiți meniul din colțul din stânga sus pentru a deschide ceva manual.", "export_deleting_old_files": "Ștergerea fi<PERSON>ierelor existente...", "loading_step_detect_platform": "Colectare fișiere și detectare structurii jocului", "loading_step_generate_dummy_dll": "Generare Assemblies Mono din IL2Cpp", "loading_step_locate_key_functions": "Scanare binar IL2Cpp pentru funcții din bibliotecă", "loose_file_saved_at": "Fișierul liber a fost salvat la {0}", "menu_language": "Limbă", "mesh_format_glb_description": "O alternativă de înaltă calitate și open-source la FBX. Versiunea binară a GLTF. Conține doar datele mesh-ului. Poate cauza erori. Unity nu poate importa resurse de acest tip.", "no_game_files_found": "Nu au fost găsite fișiere de joc Unity sau bundle-uri de resurse în fișierele selectate.", "save": "Salvează", "sprite_format_texture_description": "\"Exportă ca imagine a sprite-ului. Poate fi vizualizat în afara Unity, dar mai lent la export.", "sprite_format_yaml": "Yaml", "script_content_level_0_description": "Scripturile nu sunt exportate.", "script_content_level_2_description": "Implicit. Acest nivel exportează metode complete pentru jocurile Mono și metode fictive pentru jocurile IL2Cpp.", "script_export_format_decompiled_description": "Decompilatorul ILSpy este folosit pentru a genera scripturi CS. Acesta este de încredere. Totuși, este și consumator de timp și conține multe erori de compilare.", "sprite_format_yaml_description": "Exportă ca assets yaml care pot fi vizualizate în editor. Acesta este singurul mod care asigură o recuperare precisă a tuturor metadatelor sprite-urilor.", "view_loaded_files": "Vizualizează fișierele încărcate", "enable_static_mesh_separation": "Activeaza Separare Static Mesh", "script_export_format_dll_without_renaming_description": "Asamblările sunt exportate în forma lor compilat DLL. Unity va suprascrie probabil asamblările speciale cum ar fi Assembly-CSharp.", "export_primary_content": "Exportați conținutul principal", "export_unity_project": "Exporteaza Proiectul Unity", "stack_trace": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>i", "swagger_documentation": "Documentația Swagger", "loading_step_begin_scheme_processing": "Pornirea procesării schemei", "failed_files": "Fișier<PERSON>", "c_sharp_langage_version_config_12_0": "C# 12", "configuration_files_singletons": "Singletons", "not_available_in_the_free_edition": "Nu este disponibil în ediția gratuită.", "open_api_json": "OpenAPI JSON", "asset_tab_video": "Video", "asset_tab_model": "Model"}