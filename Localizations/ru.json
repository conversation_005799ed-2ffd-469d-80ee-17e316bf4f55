{"asset_tab_audio": "Аудио", "asset_tab_audio_duration_unknown": "<Неизвестно>", "asset_tab_audio_pause": "Пауза", "asset_tab_audio_play": "Воспроизвести", "asset_tab_basic": "Основная информация", "asset_tab_image": "Изображение", "asset_tab_text": "Текст", "asset_tab_yaml": "Yaml", "audio_export_title": "Формат экспорта Аудио", "audio_format_default": "По умолчанию", "audio_format_default_description": "Экспорт активов как типа контента, встроенного в FSB. Большинство типов аудио экспортируются как WAV, некоторые экспортируются как OGG.", "audio_format_force_wav": "Конвертировать в WAV", "audio_format_force_wav_description": "Преобразование всех аудиофайлов в файлы WAV. Не рекомендуется при импорте в Unity, так как это может привести к повторному сжатию файлов, что приведет к потере качества.", "audio_format_native": "Необработанный", "audio_format_native_description": "Необработанный звук FSB. Невозможно импортировать в Unity, поэтому используйте его, только если вы опытный пользователь.", "audio_format_yaml": "Yaml", "audio_format_yaml_description": "Экспортируйте как ассет yaml и файл resS. Это безопасный вариант и запасной вариант, когда что-то пойдет не так.", "bundled_assets_export_direct_export": "Прямой Экспорт", "bundled_assets_export_direct_export_description": "Связанные ассеты экспортируются без группировки.", "bundled_assets_export_group_by_asset_type": "Группировать по типу ассетов", "bundled_assets_export_group_by_asset_type_description": "Связанные ассеты обрабатываются так же, как ресурсы из других файлов.", "bundled_assets_export_group_by_bundle_name": "Группировать по названию бандла", "bundled_assets_export_group_by_bundle_name_description": "Связанные ассеты сгруппированы по имени их пакета ассетов.", "bundled_assets_export_title": "Режим экспорта связанных ассетов", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "Автоматический - Экспериментальный", "c_sharp_langage_version_config_auto_safe": "Автоматический - Безопасный", "c_sharp_langage_version_config_latest": "Последний C#", "c_sharp_language_version_config_description": "Версия языка C#, используемая при декомпиляции скриптов.", "check_log_for_more_details": "Проверьте логи для более подробной информации", "config_options": "Настройки конфигурации", "config_screen_drag_drop_prompt": "Обратите внимание, что некоторые изменения настроек могут вызвать или предотвратить ошибки.\n Когда будете готовы, перетащите файл/папку с игрой в это окно или воспользуйтесь меню в левом верхнем углу, чтобы открыть что-нибудь вручную.", "enable_prefab_outlining": "Включить обводку префабов", "enable_static_mesh_separation": "Включить разделение статических мешей", "error": "Ошибка", "error_exporting_with_reason": "Не удалось экспортировать игровой контент: {0}", "error_importing_with_reason": "Не удалось загрузить игровой контент: {0}", "export_complete": "Экспорт Завершён!", "export_deleting_old_files": "Удаление существующих файлов...", "export_in_progress": "Экспорт файлов\n {0}%\n {1}/{2}", "export_in_progress_no_file_count_yet": "Экспорт файлов\n 0,0%\n ?/?", "export_preparing": "Подготовка к экспорту...\n Это может занять минуту.", "ignore_engine_assets": "Игнорировать ассеты движка", "image_export_title": "Формат экспорта изображений", "image_format_description": "Влияет на все экспортированные изображения.", "loading_game_content_from": "Загрузка контента игры из {0}\n{1}", "loading_step_begin_scheme_processing": "Запуск обработки схемы", "loading_step_create_file_collection": "Создание коллекции файлов", "loading_step_detect_platform": "Сбор файлов и определение структуры игры", "loading_step_generate_dummy_dll": "Создание Mono Assemblies из IL2CPP", "loading_step_initialize_layout": "Инициализация макета ассетов", "loading_step_load_assemblies": "Загрузка скриптов", "loading_step_load_assets_from_file": "Загрузка ассетов из {0}", "loading_step_locate_key_functions": "Сканирование двоичного файла IL2CPP для библиотечных функций", "loading_step_parse_archive": "<PERSON><PERSON><PERSON><PERSON><PERSON> архива {0}", "loading_step_parse_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON> бандла {0}", "loading_step_parse_il2cpp_metadata": "Ана<PERSON><PERSON><PERSON> Метаданных IL2CPP", "loading_step_parse_resource": "Ана<PERSON>из файла ресурсов {0}", "loading_step_parse_serialized": "Анализ Сериализованного файла {0}", "loading_step_parse_web": "Ана<PERSON><PERSON>з веб-файла {0}", "loading_step_pre_processing": "Предварительная обработка файлов", "loading_step_restore_attributes": "Восстановление атрибутов сгенерированных файлов", "loose_file_saved_at": "Свободный файл сохранен в {0}", "menu_export": "Экспорт", "menu_export_all": "Экспорт всех файлов", "menu_export_selected": "Экспортировать Выбранные Файлы", "menu_export_selected_type": "Экспортировать Файлы Выбранного Типа", "menu_file": "<PERSON>а<PERSON><PERSON>", "menu_file_exit": "Выйти", "menu_file_open_file": "Открыть файл", "menu_file_open_folder": "Открыть папку", "menu_file_reset": "Сбросить", "menu_language": "Язык", "mesh_export_title": "Формат экспорта Мешей", "mesh_format_glb": "GLB", "mesh_format_glb_description": "Высококачественная альтернатива FBX с открытым исходным кодом. Бинарная версия GLTF. Содержит только данные сетки. Может вызвать ошибки. Unity не может импортировать ассеты этого типа.", "mesh_format_native": "Yaml", "mesh_format_native_description": "Надежный формат для использования meshes в редакторе. Может быть преобразован в другие форматы с помощью различных пакетов Unity.", "no_game_files_found": "В данных файлах не было найдено ни игры, ни набора ресурсов Unity.", "not_implemented_yet": "Еще не реализовано", "script_content_level_0": "Уровень 0", "script_content_level_0_description": "Скрипты не экспортируются.", "script_content_level_1": "Уровень 1", "script_content_level_1_description": "Методы удаляются из декомпилированного экспорта.", "script_content_level_2": "Уровень 2", "script_content_level_2_description": "По умолчанию. Это экспортирует полные методы для игр Mono и методы пустышки для игр IL2Cpp.", "script_content_level_3": "Уровень 3", "script_content_level_3_description": "Методы IL2Cpp безопасно восстанавливаются, где это возможно.", "script_content_level_4": "Уровень 4", "script_content_level_4_description": "Методы IL2Cpp восстанавливаются без учета безопасности.", "script_content_level_title": "Уровень контента скриптов", "script_export_format_decompiled": "Декомпиляция", "script_export_format_decompiled_description": "Декомпилятор ILSpy используется для генерации скриптов CS. Это надежно. Однако это также отнимает много времени и содержит много ошибок компиляции.", "script_export_format_dll_with_renaming": "Экспорт Dll с переименованием", "script_export_format_dll_without_renaming": "Экспорт Dll без переименования", "script_export_format_dll_without_renaming_description": "Сборки экспортируются в скомпилированном виде Dll. Экспериментальный. Может вообще не работать.", "script_export_format_hybrid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "script_export_title": "Формат экспорта скриптов", "script_language_version_title": "Версия языка С#", "select_asset_to_view_info": "Выберите файл слева, чтобы просмотреть информацию о нем.", "shader_asset_export_title": "Формат экспорта шейдеров", "shader_asset_format_decompile": "Декомпиляция", "shader_asset_format_decompile_description": "Экспортируйте шейдер в ShaderLab. Очень экспериментально и почти наверняка будут ошибки. Поддерживает только DX11, а не DX9.", "shader_asset_format_disassembly": "Дизассемблированный", "shader_asset_format_disassembly_description": "Экспортируйте шейдер как дизассемблированный. Экспериментальный и склонный к поломке. Не компилируется в редакторе.", "shader_asset_format_dummy": "Шейдер пустышка", "shader_asset_format_dummy_description": "Экспортирует шейдер как пустышку. Несмотря на то, что он сохраняет такие данные, как Properties и FallBack, он использует общий непрозрачный код шейдера.", "shader_asset_format_yaml": "<PERSON><PERSON><PERSON>айл", "shader_asset_format_yaml_description": "Экспортируйте шейдер как yaml asset. Экспериментальный и пригодный только для просмотра в редакторе. Редактор может случайным образом повредить эти файлы.", "skip_streaming_assets": "Пропустить папку StreamingAssets", "sprite_export_title": "Формат экспорта спрайтов", "sprite_format_native": "Unity", "sprite_format_native_description": "Экспорт в формате спрайта Unity. Нельзя просмотреть вне Unity.", "sprite_format_texture": "Текстуры", "sprite_format_texture_description": "Экспортируйте как изображение листа спрайтов. Можно просматривать за пределами Unity, но медленнее экспортировать.", "sprite_format_yaml": "Yaml", "sprite_format_yaml_description": "Экспортируйте как ассеты yaml, которые можно просмотреть в редакторе. Это единственный режим, обеспечивающий точное восстановление всех метаданных спрайтов.", "success": "Готово!", "terrain_export_title": "Формат экспорта Terrain", "terrain_format_heatmap": "Карта высот", "terrain_format_heatmap_description": "Экспортируйте тепловую карту высоты местности в каждом месте. Только действительно полезно, если вы не заботитесь о деталях или о местности в 3D.", "terrain_format_mesh": "3D Меш", "terrain_format_mesh_description": "Экспортируйте ландшафт в виде 3D-сетки в формате GLB, который подходит для просмотра в различных 3D-редакторах.", "terrain_format_native": "Yaml", "terrain_format_native_description": "Экспорт в собственный формат ландшафта Unity. Самый полезный вариант, если вы планируете повторно импортировать обратно в Unity.", "text_asset_export_title": "Формат экспорта TextAsset", "text_asset_format_binary": "<PERSON>а<PERSON><PERSON>ы", "text_asset_format_binary_description": "Экспортируйте необработанные байты текстового ассета с расширением .bytes.", "text_asset_format_parse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text_asset_format_parse_description": "Экспортируйте как обычный текстовый файл, но попытается угадать правильное расширение файла (например, файлы JSON получают расширение .json).", "text_asset_format_text": "Обычный текст", "text_asset_format_text_description": "Экспорт в виде текстового файла (.txt).", "welcome_title": "Добро пожаловать в AssetRipper", "width": "Ши<PERSON><PERSON><PERSON>", "game_object": "Игровой объект", "donation_message": "Если Вам понравился Asset Ripper, вы можете поддержать проект:", "name": "Имя", "no_files_loaded": "Файлы не загружены", "shader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "class": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collection": "Коллекция", "collections": "Коллекции", "channels": "Каналы", "count": "Количество", "csharp_type": "Тип C#", "default_version": "Изначальная версия", "height": "Высота", "home": "До<PERSON><PERSON><PERSON>", "namespace": "Пространство имен", "path": "Путь", "privacy": "Приватность", "resources": "Ресурсы", "save": "Сохранить", "scene": "Сцена", "script": "Скрипт", "menu_load": "Загрузка", "menu_view": "Вид", "original_path": "Исходный путь", "replace": "Заменить", "save_settings_to_disk": "Сохранить настройки на диск", "parent": "Родитель", "remove": "Удалить", "select_folder": "Выбор папки", "settings": "Настройки", "size": "Размер", "view_loaded_files": "Просмотр загруженных файлов", "settings_can_only_be_changed_before_loading_files": "Настройки могут быть изменены только перед загрузкой файлов.", "menu_import": "Импорт", "no_data_has_been_loaded_for_this_key": "Для этого ключа не было загружено никаких данных.", "select_file": "Выбор файла", "texture": "Текстура", "there_are_no_keys_for_this_data_type": "Нет ключей для этого типа данных.", "warning_this_directory_is_not_empty_all_content_will_be_deleted": "Предупреждение: эта директория не пустая. Всё содержимое будет удалено.", "an_error_occured_during_decompilation": "Во время декомпиляции произошла ошибка.", "appreciation_message": "Благодарим вас за поддержку AssetRipper!", "asset_tab_dependencies": "Зависимости", "asset_tab_font": "<PERSON>ри<PERSON><PERSON>", "commands": "Команды", "experimental": "Экспериментально", "format": "Формат", "frequency": "Частота", "length": "Длина", "licenses": "Лицензии", "load": "Загрузка", "configuration_files": "Конфигурационные файлы", "configuration_files_lists": "Списки", "welcome": "Добро пожаловать", "assembly_name": "Название Сборки", "asset_tab_development": "Разработка", "asset_tab_information": "Информация", "assets": "Ресурсы", "bundle": "Пак<PERSON>т", "bundles": "Пакеты", "class_name": "Имя Класса", "data": "Данные", "asset_tab_video": "Видео"}