{"asset_ripper_free": "AssetRipper ฟรี", "asset_ripper_premium": "AssetRipper ขั้นสูง", "asset_tab_audio_duration_unknown": "<ไม่ทราบ>", "an_error_occured_during_decompilation": "เกิดข้อผิดพลาดระหว่างการถอดรหัส", "asset_tab_audio_pause": "หยุดชั่วคราว", "asset_tab_font": "ฟ้อน", "asset_tab_image": "ภาพ", "asset_tab_model": "3D", "asset_tab_text": "ข้อความ", "audio_export_title": "รูปแบบการส่งออกเสียง", "audio_format_force_wav": "บันทึกเป็น WAV", "asset_tab_development": "การพัฒนา", "audio_format_native": "Raw", "assembly_name": "ภาษา Assembly", "asset_bundle_name": "<PERSON><PERSON>", "asset_tab_hex": "เลขฐาน 16", "asset_tab_dependencies": "การพึ่งพา", "assets": "Assets", "audio_clip": "AudioClip", "bundles": "Bundles", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "อัตโนมัติ - ทดลอง", "c_sharp_langage_version_config_latest": "C# ล่าสุด", "c_sharp_language_version_config_description": "เวอร์ชันภาษา C# ที่จะใช้ในการถอดรหัสสคริปต์", "channels": "ช่อง", "data": "ข้อมูล", "error": "ล้มเหลว", "json": "Json", "load": "โหลด", "loading_step_load_assemblies": "กำลังโหลดส่วนประกอบ", "menu_export": "ส่งออก", "menu_file": "ไฟล์", "menu_file_exit": "ออก", "menu_file_open_file": "เปิดไฟล์", "menu_file_open_folder": "เปิดโฟลเดอร์", "menu_file_reset": "คืนค่าเริ่มต้น", "menu_import": "นำเข้า", "menu_language": "ภาษา", "menu_view": "ภาพรวม", "mesh": "<PERSON><PERSON>", "mesh_export_title": "รูปแบบการส่งออก Mesh", "mesh_format_glb": "GLB", "mesh_format_native": "Yaml", "save": "บันทึก", "remove": "ลบ", "scene": "Scene", "script": "<PERSON><PERSON><PERSON>", "asset_tab_audio": "เสียง", "appreciation_message": "ขอบคุณที่สนับสนุนโครงการของเรา", "asset_tab_audio_play": "เล่น", "audio_format_default": "ค่าเริ่มต้น", "c_sharp_langage_version_config_1": "C# 1", "asset_tab_information": "ข้อมูล", "bundled_assets_export_title": "วิธีการส่งออก", "c_sharp_langage_version_config_auto_safe": "อัตโนมัติ - ปลอดภัย", "csharp_type": "ประเภท C#", "format": "รูปแบบ", "licenses": "ใบอนุญาต", "menu_export_all": "ส่งออกทุกไฟล์", "menu_export_selected": "ส่งออกไฟล์ที่เลือก", "menu_load": "โหลด", "name": "ชื่อ", "no_files_loaded": "ไม่มีไฟล์ที่โหลด", "bundle": "Bundle", "audio_format_default_description": "ส่งออกสินทรัพย์เป็นประเภทเนื้อหาที่ฝังอยู่ภายในไฟล์ FSB ไฟล์เสียงส่วนใหญ่จะถูกส่งออกในรูปแบบ WAV ในขณะที่บางประเภทจะถูกส่งออกเป็น OGG", "audio_format_force_wav_description": "แปลงไฟล์เสียงทั้งหมดเป็นไฟล์ WAV ไม่แนะนำหากต้องการนำเข้าไฟล์ไปยัง Unity เนื่องจาก Unity อาจบีบอัดไฟล์ใหม่ ซึ่งอาจทำให้คุณภาพเสียงลดลง", "audio_format_native_description": "ไฟล์เสียงแบบ Raw FSB ไม่สามารถนำเข้าใน Unity ได้ ใช้เฉพาะในกรณีที่เป็นผู้ใช้งานขั้นสูงเท่านั้น", "audio_format_yaml": "Yaml", "audio_format_yaml_description": "ส่งออกเป็นไฟล์สินทรัพย์ Yaml และไฟล์ resS ตัวเลือกนี้ปลอดภัยและใช้เป็นแผนสำรองเมื่อเกิดปัญหา", "bundled_assets_export_direct_export": "ส่งออกโดยตรง", "bundled_assets_export_direct_export_description": "สินทรัพย์ที่ถูกรวมไว้จะถูกส่งออกโดยไม่มีการจัดกลุ่ม", "bundled_assets_export_group_by_asset_type": "จัดกลุ่มตามประเภทสินทรัพย์", "bundled_assets_export_group_by_asset_type_description": "สินทรัพย์ที่ถูกรวมไว้จะถูกจัดการเหมือนกับสินทรัพย์จากไฟล์อื่นๆ", "bundled_assets_export_group_by_bundle_name": "จัดกลุ่มตามชื่อของบันเดิล", "bundled_assets_export_group_by_bundle_name_description": "สินทรัพย์ที่ถูกรวมไว้จะถูกจัดกลุ่มตามชื่อของบันเดิลสินค้า", "check_log_for_more_details": "ตรวจสอบบันทึกเพื่อดูรายละเอียดเพิ่มเติม", "class": "Class", "class_id_type_name": "Class ID Type Name", "class_id_type_number": "Class ID Type Number", "class_name": "Class Name", "collection": "สะสม", "shader_asset_format_dummy": "Dummy <PERSON>", "sprite_format_native": "Unity", "skip_streaming_assets": "ข้ามโฟลเดอร์ StreamingAssets", "size": "ขนาด", "shader_asset_format_yaml": "<PERSON><PERSON><PERSON>", "text_asset_export_title": "รูปแบบการส่งออกของ TextAsset", "terrain_format_native_description": "ส่งออกในรูปแบบเทอร์เรนพื้นฐานของ Unity เป็นตัวเลือกที่มีประโยชน์ที่สุดหากคุณวางแผนที่จะนำเข้าไฟล์กลับไปยัง Unity อีกครั้ง", "sprite_export_title": "รูปแบบการส่งออกของ Sprite", "shader_asset_format_yaml_description": "ส่งออกเชเดอร์เป็นไฟล์สินทรัพย์ YAML เป็นฟีเจอร์ทดลองและสามารถใช้งานได้เฉพาะในตัวแก้ไขเท่านั้น โดยตัวแก้ไขอาจทำให้ไฟล์เหล่านี้เสียหายได้โดยบังเอิญ", "shader_asset_format_dummy_description": "ส่งออกเชเดอร์เป็นเชเดอร์จำลอง ถึงแม้ว่าจะรักษาข้อมูลบางอย่างเช่น พร็อพเพอร์ตี้และฟอลล์แบ็ค แต่จะใช้โค้ดเชเดอร์ทั่วไปที่เป็นโปร่งแสง", "config_screen_drag_drop_prompt": "โปรดทราบว่าการเปลี่ยนแปลงการตั้งค่าบางอย่างอาจทำให้เกิดหรือป้องกันข้อผิดพลาดได้\nเมื่อคุณพร้อมแล้ว ให้ลากและวางไฟล์/โฟลเดอร์เกมของคุณลงในหน้าต่างนี้ หรือใช้เมนูที่ด้านซ้ายบนเพื่อเปิดบางอย่างด้วยตนเอง"}