{"asset_tab_audio": "Ses", "asset_tab_audio_duration_unknown": "<Bilinmiyor>", "asset_tab_audio_pause": "<PERSON><PERSON><PERSON>", "asset_tab_audio_play": "<PERSON><PERSON><PERSON>", "asset_tab_basic": "<PERSON><PERSON>", "asset_tab_image": "Resim", "asset_tab_text": "<PERSON><PERSON>", "asset_tab_yaml": "Yaml", "audio_export_title": "Ses Dışa Aktarma Formatı", "audio_format_default": "Varsayılan", "audio_format_default_description": "Assetleri FSB'nin içine gömülü içerik türü olarak dışa aktarın. Çoğu ses türü WAV olarak, bazıları OGG olarak dışa aktarılır.", "audio_format_force_wav": "WAV'a Dönüştür", "audio_format_force_wav_description": "Tüm ses dosyalarını WAV'a dönüştür. Tekrar sıkıştırma kalite kaybına neden olabileceğinden Unity'ye aktarılması önerilmez.", "audio_format_native": "Ham", "audio_format_native_description": "Ham FSB Ses. Unity'ye aktarılamaz, dolayısıyla bunu ileri düzey kullanıcıysanız kullanın.", "audio_format_yaml": "Yaml", "audio_format_yaml_description": "Yaml asset ve resS dosyası olarak dışa aktarın. Bu güvenli bir seçenektir ve işler ters giderse yedektedir.", "bundled_assets_export_direct_export": "<PERSON><PERSON><PERSON><PERSON> Dışa Aktar", "bundled_assets_export_direct_export_description": "Paketlenmiş assetler gruplandırılmadan dışa aktarılır.", "bundled_assets_export_group_by_asset_type": "<PERSON><PERSON> G<PERSON>", "bundled_assets_export_group_by_asset_type_description": "Paketlenmiş assetler diğer dosyalardaki assetlerle aynı işlemden geçirilir.", "bundled_assets_export_group_by_bundle_name": "Paket Adına Göre Grupla", "bundled_assets_export_group_by_bundle_name_description": "Paketlenmiş assetler, asset paket adlarına göre gruplandırılır.", "bundled_assets_export_title": "Paketlenmiş Assetleri Dışa Aktarma Modu", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "Otomatik - Deneysel", "c_sharp_langage_version_config_auto_safe": "Otomatik - Güvenli", "c_sharp_langage_version_config_latest": "C# Son Sürüm", "c_sharp_language_version_config_description": "Kodlar derlenirken kullanılacak C# dil sürümü.", "check_log_for_more_details": "Ayrıntılar için kaydı inceleyebilirsiniz", "config_options": "Ya<PERSON><PERSON><PERSON><PERSON><PERSON>", "config_screen_drag_drop_prompt": "Bazı ayar değişikliklerinin hatalara neden olabileceğini veya hataları önleyebileceğini lütfen unutmayın.\n<PERSON><PERSON><PERSON>r <PERSON>uğ<PERSON>uz<PERSON>, oyun dosyanızı/klasörünüzü bu pencereye sürükleyip bırakın veya manuel olarak açmak için sol üstteki menüyü kullanın.", "enable_prefab_outlining": "Prefab Taslağı Etkinleştir", "enable_static_mesh_separation": "Sabit Mesh Ayırmayı Etkinleştir", "error": "<PERSON><PERSON>", "error_exporting_with_reason": "Oyun içeriği dışa aktarılamadı: {0}", "error_importing_with_reason": "<PERSON>yun içeriği yüklenemedi: {0}", "export_complete": "Dışa Aktarıldı!", "export_deleting_old_files": "Mevcut dosyalar te<PERSON>zleniyor..", "export_in_progress": "Asset Dosyaları Dışa Aktarımı\n{0}%\n{1}/{2}", "export_in_progress_no_file_count_yet": "Asset Dosyaları Dışa Aktarımı\n0.0%\n?/?", "export_preparing": "Dışa Aktarma için Ha<PERSON>ı<PERSON>ıyor..\nBu bir dakika sürebilir.", "image_export_title": "Resim Dışa Aktarma Formatı", "image_format_description": "Dışa aktarılan tüm resimleri etkiler", "loading_game_content_from": "{0} İçeriği Yükleniyor\n{1}", "loading_step_begin_scheme_processing": "Şema İşleme Başlatılıyor", "loading_step_create_file_collection": "<PERSON><PERSON><PERSON>u Oluşturuluyor", "loading_step_detect_platform": "Dosyaları Toplama ve Oyun Yapısını Tespit Etme", "loading_step_generate_dummy_dll": "IL2CPP'den Mon<PERSON>", "loading_step_initialize_layout": "Asset Düzeni Hazırlanıyor", "loading_step_load_assemblies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loading_step_load_assets_from_file": "{0}'dan <PERSON>'ler <PERSON>", "loading_step_locate_key_functions": "IL2CPP İkilisinin Kütüphane İşlevleri için <PERSON>ı", "loading_step_parse_archive": "{0} Arşiv Dosyası Ayrıştırılıyor", "loading_step_parse_bundle": "{0} Paketi Ayrıştırılıyor", "loading_step_parse_il2cpp_metadata": "IL2CPP Meta Verileri Ayrıştırılıyor", "loading_step_parse_resource": "{0} Kaynak Dosyası Ayrıştırılıyor", "loading_step_parse_serialized": "{0} Dizeleştirilmiş Dosya Ayrıştırılıyor", "loading_step_parse_web": "{0} Web Dosyası Ayrıştırılıyor", "loading_step_pre_processing": "<PERSON>n <PERSON>", "loading_step_restore_attributes": "Oluşturulan Assembly'lerde Öznitelikleri Geri Yükleme", "loose_file_saved_at": "<PERSON>ose dosya {0} konumuna kaydedildi", "menu_export": "Dışa aktar", "menu_export_all": "<PERSON><PERSON>m dosyaları dışa aktar", "menu_export_selected": "Seçilen Dosyayı Dışa Aktar", "menu_export_selected_type": "Seçilen Türdeki Bütün Dosyaları Dışa Aktar", "menu_file": "<PERSON><PERSON><PERSON>", "menu_file_exit": "Çıkış", "menu_file_open_file": "<PERSON><PERSON><PERSON>", "menu_file_open_folder": "Klasör <PERSON>", "menu_file_reset": "Sıfırla", "menu_language": "Dil", "mesh_export_title": "Mesh Dışa Aktarma Formatı", "mesh_format_glb": "GLB", "mesh_format_glb_description": "FBX'e yüks<PERSON> ka<PERSON>, açık kaynaklı bir alternatif. GLTF'nin binary sürümü. Sadece mesh verisi içerir. Hatalara neden olabilir. Unity bu tür assetleri içe aktaramaz.", "mesh_format_native": "Yaml", "mesh_format_native_description": "Düzenleyicide mesh kullanmak için güçlü bir format. Çeşitli Unity paketleri diğer formatlara dönüştürülebilir.", "no_game_files_found": "Dosyalarda Unity oyunu veya asset paketi bulunamadı.", "not_implemented_yet": "Henüz <PERSON>ygulanmadı", "script_content_level_0": "Seviye 0", "script_content_level_0_description": "<PERSON><PERSON>ler dışa aktarılmaz.", "script_content_level_1": "Seviye 1", "script_content_level_1_description": "Met<PERSON><PERSON> derlenmemiş dışa aktarımdan <PERSON>ılı<PERSON>.", "script_content_level_2": "Seviye 2", "script_content_level_2_description": "Varsayılan. Mono oyunlar için tam yöntemleri ve IL2Cpp oyunlar için yapay yöntemleri dışa aktarır.", "script_content_level_3": "Seviye 3", "script_content_level_3_description": "IL2Cpp metodları mümkün olan yerlerde güvenle kurtarılır.", "script_content_level_4": "Seviye 4", "script_content_level_4_description": "IL2Cpp yöntemleri, güvenlik göz ardı edilerek kurtarılır.", "script_content_level_title": "Script İçerik Seviyesi", "script_export_format_decompiled": "<PERSON><PERSON>miş", "script_export_format_decompiled_description": "CS komut dosyaları oluşturmak için ILSpy derleyicisi kullanılır. Güvenlidir ancak, aynı zamanda zaman kaybına neden olur ve birçok hata içerir.", "script_export_format_dll_with_renaming": "Yeniden Adlandırmayla Dll Dışa Aktarma", "script_export_format_dll_without_renaming": "Yeniden Adlandırmadan Dll Dışa Aktarma", "script_export_format_dll_without_renaming_description": "Assembly'ler <PERSON> Dll formunda dışa aktarılır. Deneyseldir. Hiç çalışmayabilir.", "script_export_format_hybrid": "Hibrit", "script_export_title": "Script Dışa Aktarma Formatı", "script_language_version_title": "C# Dil Sürümü", "select_asset_to_view_info": "Hakkında bilgi görüntülemek için sol taraftan Asset seçin", "shader_asset_export_title": "Gölgelendirici Dışa Aktarma Formatı", "shader_asset_format_decompile": "<PERSON><PERSON><PERSON><PERSON>", "shader_asset_format_decompile_description": "Gölgelendiriciyi ShaderLab'a aktarın. Oldukça deney<PERSON>dir ve neredeyse hatalar kaçınılmazdır. Sadece DX11'i destekler, DX9'u desteklemez.", "shader_asset_format_disassembly": "Söküm", "shader_asset_format_disassembly_description": "Gölgelendiriciyi söküm olarak dışa aktarın. Deneyseldir ve bozulabilir. Bu, düzenleyicide derlenmez.", "shader_asset_format_dummy": "<PERSON><PERSON><PERSON>", "shader_asset_format_dummy_description": "Gölgelendiriciyi yapay gölgelendirici olarak dışa aktarın. Özellikler ve FallBack gibi verileri korur ancak genel, opak gölgelendirici kodu kullanır.", "shader_asset_format_yaml": "<PERSON><PERSON><PERSON>", "shader_asset_format_yaml_description": "Gölgelendiriciyi yaml asset olarak dışa aktarın. Deneyseldir ve yalnızca düzenleyicide görüntülemek için kullanılabilir. Düzenleyici bu dosyaları rastgele bozabilir.", "skip_streaming_assets": "StreamingAssets Klasörünü Atla", "sprite_export_title": "Sprite Dışa Aktarma Formatı", "sprite_format_native": "Unity", "sprite_format_native_description": "Unity sprite formatında dışa aktarın. Unity haricinde görüntülenemez.", "sprite_format_texture": "Texture", "sprite_format_texture_description": "Sprite Sheet'in resmi olarak dışa aktarın. Unity haricinde görüntülenebilir, fakat dışa aktarması daha yavaştır.", "sprite_format_yaml": "Yaml", "sprite_format_yaml_description": "Düzenleyicide görülebilir yaml assetleri olarak dışa aktarın. Bu, spriteların tüm meta verilerinin eksiksiz kurtarılmasını sağlayan tek moddur.", "success": "Başarılı!", "terrain_export_title": "Arazi Dışa Aktarma Formatı", "terrain_format_heatmap": "Yükseklik Haritası", "terrain_format_heatmap_description": "Her konumdaki arazi yüksekliğinin ısı haritasını dışa aktarın. Sadece ayrıntıları veya arazinin 3D olmasını önemsemiyorsanız çok kullanışlıdır.", "terrain_format_mesh": "3D Mesh", "terrain_format_mesh_description": "<PERSON><PERSON><PERSON>, çeşitli 3D düzenleyicilerle görüntülemeye uygun GLB formatında 3D mesh olarak dışa aktarın.", "terrain_format_native": "Yaml", "terrain_format_native_description": "Yerel Unity arazi formatında dışa aktarın. Unity'ye tekrar aktarmak için en kullanışlı seçenek.", "text_asset_export_title": "TextAsset Dışa Aktarma Formatı", "text_asset_format_binary": "<PERSON><PERSON>", "text_asset_format_binary_description": "Metin asset'inin ham baytlarını .bytes uzantısı ile dışa aktarın.", "text_asset_format_parse": "Ayrıştırma", "text_asset_format_parse_description": "<PERSON><PERSON><PERSON> metin dosyası olarak dışa aktarın, ancak doğru dosya uzantısını tahmin etmeye çalışın (örneğin JSON dosyaları .json uzantısına sahip olur)", "text_asset_format_text": "<PERSON><PERSON><PERSON>", "text_asset_format_text_description": "<PERSON><PERSON><PERSON> metin dos<PERSON> (.txt) olarak dışa aktar", "welcome_title": "AssetRipper'a <PERSON>"}