{"asset_tab_audio": "Ау<PERSON><PERSON><PERSON>", "asset_tab_audio_duration_unknown": "<Невідомо>", "asset_tab_audio_pause": "Пауза", "asset_tab_audio_play": "<PERSON>ра<PERSON>и", "asset_tab_basic": "Основна інформація", "asset_tab_image": "Зображення", "asset_tab_text": "Текст", "asset_tab_yaml": "YAML", "audio_export_title": "Формат експорту аудіо", "bundled_assets_export_direct_export": "Прямий експорт", "bundled_assets_export_group_by_asset_type": "Групувати за типом асета", "bundled_assets_export_group_by_asset_type_description": "Bundled assets обробляються так само як і ресурси з інших файлів.", "bundled_assets_export_group_by_bundle_name": "Групувати за назвою пакета", "bundled_assets_export_group_by_bundle_name_description": "Ассети згруповано за їх назвами asset bundle.", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "Автоматично - Експериментальна", "c_sharp_langage_version_config_auto_safe": "Автоматично - Безпечна", "c_sharp_langage_version_config_latest": "C# Остання", "c_sharp_language_version_config_description": "Версія C# яка буде використана під час декомпіляції скриптів.", "config_options": "Параметри конфігурації", "enable_static_mesh_separation": "Увімкнути відділення статичних мешів", "error": "Помилка", "error_exporting_with_reason": "Не вдалося експортувати вміст гри: {0}", "error_importing_with_reason": "Не вдалося завантажити вміст гри: {0}", "export_complete": "Експорт завершено!", "export_deleting_old_files": "Очищення існуючих файлів...", "export_in_progress": "Експорт асетів\n{0}%\n{1}/{2}", "export_in_progress_no_file_count_yet": "Експорт асетів\n0,0%\n?/?", "export_preparing": "Підготовка до експорту...\nЦе може зайняти хвилину.", "ignore_engine_assets": "Ігнорувати ассети рушія", "image_export_title": "Формат експорту зображень", "loading_game_content_from": "Завантаження ігрового вмісту з {0}\n{1}", "loading_step_begin_scheme_processing": "Початок обробки схеми", "loading_step_create_file_collection": "Створення колекції файлів", "loading_step_detect_platform": "Збір файлів і визначення структури гри", "loading_step_generate_dummy_dll": "Генерація збірок Mono з IL2CPP", "loading_step_initialize_layout": "Ініціалізація макету асетів", "loading_step_load_assemblies": "Завантаження Assemblies", "loading_step_load_assets_from_file": "Завантаження Асетів із {0}", "loading_step_locate_key_functions": "Сканування двійкового файлу IL2CPP на наявність бібліотечних функцій", "loading_step_parse_archive": "Парсинг архівного файлу {0}", "loading_step_parse_bundle": "Парсинг пакета {0}", "loading_step_parse_il2cpp_metadata": "Аналіз метаданих IL2CPP", "loading_step_parse_resource": "Парсинг файлу ресурсів {0}", "loading_step_parse_serialized": "Парсинг серіалізованого файлу {0}", "loading_step_parse_web": "Парсинг Веб-файлу {0}", "loading_step_pre_processing": "Попередня обробка файлів", "loading_step_restore_attributes": "Відновлення атрибутів згенерованих Assemblies", "menu_export": "Експорт", "menu_export_all": "Експортувати всі файли", "menu_export_selected": "Експортувати вибраний файл", "menu_export_selected_type": "Експортувати всі файли вибраного типу", "menu_file": "<PERSON>а<PERSON><PERSON>", "menu_file_exit": "Вийти", "menu_file_open_file": "Відкрити файл", "menu_file_open_folder": "Відкрити папку", "menu_file_reset": "Скинути", "menu_language": "Мова", "mesh_export_title": "Формат експорту мешу", "no_game_files_found": "У зазначених вами файлах не знайдено Unity гри або asset bundle.", "script_content_level_title": "Рівень вмісту скриптів", "script_export_title": "Формат експорту скриптів", "script_language_version_title": "Версія мови C#", "select_asset_to_view_info": "Виберіть асет ліворуч, щоб переглянути інформацію про нього", "shader_asset_export_title": "Формат експорту шейдерів", "skip_streaming_assets": "Пропустити папку StreamingAssets", "sprite_export_title": "Формат експорту спрайтів", "success": "Успіх!", "terrain_export_title": "Формат експорту Terrain", "text_asset_export_title": "Формат експорту тексту", "welcome_title": "Вітаємо в AssetRipper", "terrain_format_mesh_description": "Експортувати рельєф у вигляді 3D-сітки у форматі GLB, придатному для перегляду за допомогою широкого спектру 3D-редакторів.", "terrain_format_native": "Yaml", "stack_trace": "Відстеження стека", "terrain_format_heatmap_description": "Експортувати теплову карту висоти місцевості в кожному місці. Це дійсно корисно, якщо вам не важливі деталі або якщо ви не хочете мати місцевість у форматі 3D.", "terrain_format_mesh": "3D Mesh", "terrain_format_native_description": "Експортувати у власному форматі ландшафту Unity. Найкорисніша опція, якщо ви плануєте повторно імпортувати назад до Unity.", "override_path": "Перевизначити шлях", "script_content_level_3": "Рівень 3", "script_content_level_3_description": "Методи IL2Cpp безпечно відновлюються, де це можливо.", "script_content_level_4": "Рівень 4", "script_content_level_4_description": "Методи IL2Cpp відновлюються не дивлячись на небезпеку.", "script_export_format_dll_with_renaming": "Експорт DLL з перейменуванням", "script_export_format_dll_without_renaming": "Експорт DLL без перейменування", "script_export_format_hybrid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "select_file": "Вибер<PERSON>ть Файл", "select_folder": "Виберіть папку", "shader_asset_format_disassembly": "Дізассемблювання", "shader_asset_format_disassembly_description": "Експорт шейдерів як Disassembly. Експериментальний і схильний до поломок. Не компілюється в редакторі.", "text_asset_format_binary": "Байти", "text_asset_format_text": "Звичайний текст", "texture": "Текстура", "view_loaded_files": "Перегляд завантажених файлів", "welcome": "Ласкаво просимо", "audio_format_native_description": "Необроблений FSB Audio. Неможливо імпортувати в Unity, тому використовуйте це, лише якщо ви досвідчений користувач.", "audio_format_force_wav_description": "Конвертувати всі аудіофайли у WAV. Не рекомендується при імпорті в Unity, оскільки він може перекомпресувати файли, що призведе до втрати якості.", "script_content_level_2_description": "За замовчуванням. Це експортує повні методи для ігор Mono та фіктивні методи для ігор IL2Cpp.", "script_export_format_decompiled_description": "Декомпілятор ILSpy використовується для створення CS скриптів. Це надійно. Однак це також займає багато часу та містить багато помилок компіляції.", "script_export_format_dll_without_renaming_description": "Збірки експортуються у скомпільованому вигляді DLL. Unity, ймовірно, перезапише спеціальні збірки, такі як Assembly-CSharp.", "script_export_format_hybrid_description": "Спеціальні збірки, такі як Assembly-CSharp, декомпілюються. Інші збірки експортуються у скомпільованому вигляді DLL.", "settings": "Параметри", "shader_asset_format_decompile": "Декомпіляція", "shader_asset_format_decompile_description": "Експорт шейдеру в ShaderLab. Дуже експериментально та майже завжди буде мати помилки. Підтримує тільки DX11, не DX9.", "text_asset_format_text_description": "Експортувати як звичайний текстовий файл (.txt).", "lightmap_texture_export_title": "Формат експорту Lightmap текстури", "lightmap_texture_format_description": "Це впливає на всі експортовані lightmap текстури.", "menu_load": "Завант<PERSON><PERSON><PERSON>ти", "shader_asset_format_dummy": "Фіктивний шейдер", "size": "Розмір", "shader_asset_format_yaml_description": "Експортувати шейдер як yaml-актив. Експериментальний і придатний лише для перегляду у редакторі. Редактор може випадково пошкодити ці файли.", "failed_files": "Помилка файлів", "script_export_format_decompiled": "Декомпіляція", "shader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shader_asset_format_yaml": "<PERSON><PERSON><PERSON>в", "width": "Ши<PERSON><PERSON><PERSON>", "yaml": "Yaml", "settings_can_only_be_changed_before_loading_files": "Параметри можна змінити лише перед завантаженням файлів.", "an_error_occured_during_decompilation": "Виникла помилка під час декомпіляції.", "appreciation_message": "Дякуємо за підтримку AssetRipper!", "asset_ripper_free": "AssetRipper Free", "asset_ripper_premium": "AssetRipper Premium", "asset_tab_dependencies": "Залежності", "asset_tab_font": "<PERSON>ри<PERSON><PERSON>", "audio_format_default_description": "Експорт активів як типу вмісту, вбудованого в FSB. Більшість типів аудіо експортується як WAV, деякі експортуються як OGG.", "audio_format_yaml_description": "Експорт як ресурс yaml і файл resS. Це безпечний і резервний варіант, коли щось піде не так.", "audio_format_yaml": "YAML", "asset_tab_video": "Відео", "audio_format_default": "За замовчуванням", "audio_format_force_wav": "Конвертувати у WAV", "configuration_files": "Конфігураційні файли", "bundled_assets_export_direct_export_description": "Bundled assets експортуються без групування.", "bundled_assets_export_title": "Режим експорту Bundled Assets", "bundles": "Пакети (Bundles)", "path": "<PERSON><PERSON><PERSON><PERSON>", "experimental": "Експерементально", "guid": "GUID", "height": "Висота", "mesh_format_native_description": "Надійний формат для використання мешу в редакторі. Можна конвертувати в інші формати різними пакетами Unity.", "script_content_level_0_description": "Скрипти не експортуються.", "sprite_format_yaml": "Yaml", "audio_format_native": "Необроблений", "c_sharp_langage_version_config_12_0": "C# 12", "channels": "Канали", "check_log_for_more_details": "Перевірте журнал з логами для детальної інформації", "class_id_type_name": "Назва типу ідентифікатора класу", "config_screen_drag_drop_prompt": "Зауважте, що деякі зміни налаштувань можуть спричинити або запобігти помилкам.\nКоли ви будете готові, перетягніть файл/папку вашої гри в це вікно або скористайтеся меню у верхньому лівому куті, щоб відкрити щось вручну.", "configuration_files_lists": "Списки", "configuration_files_singletons": "Синглтони", "count": "Кількість", "csharp_type": "Тип C#", "data": "<PERSON><PERSON><PERSON><PERSON>", "default_version": "Версія за замовчуванням", "donation_message": "Якщо Вам сподобався AssetRipper, ви можете підтримати проект:", "enable_asset_deduplication": "Увімкнути дедублікацію активів", "enable_prefab_outlining": "Увімкнути Prefab Outlining", "format": "Формат", "frequency": "Частота", "game_object": "Ігровий об'єкт (GameObject)", "home": "Додому", "image_format_description": "Це впливає на всі експортовані зображення.", "length": "Довжина", "licenses": "Ліцензії", "load": "Завант<PERSON><PERSON><PERSON>ти", "json": "Jsоn", "loose_file_saved_at": "Відсутній файл збережено в {0}", "mesh_format_glb_description": "Високоякісна альтернатива FBX з відкритим кодом. Бінарна версія GLTF. Містить лише дані мешу. Може викликати помилки. Unity не може імпортувати активи цього типу.", "mesh_format_native": "<PERSON><PERSON>", "name": "Ім'я", "not_available_in_the_free_edition": "Недоступно у безкоштовній версії.", "script_content_level_1_description": "Методи видаляються з декомпільованого експорту.", "script_content_level_2": "Рівень 2", "text_asset_format_parse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text_asset_format_parse_description": "Експортувати як звичайний текстовий файл, але спробуйте вгадати правильне розширення файлу (наприклад, JSON-файли мають розширення .json).", "shader_asset_format_dummy_description": "Експортувати шейдер як фіктивний шейдер. Хоча при цьому зберігаються такі дані, як властивості та запасні варіанти, використовується загальний, непрозорий код шейдера.", "sprite_format_native": "Unity", "sprite_format_native_description": "Експорт у форматі спрайту Unity. Неможливо переглянути за межами Unity.", "sprite_format_texture": "Текстура", "sprite_format_texture_description": "Експортувати як зображення аркуша спрайтів. Можна переглядати за межами Unity, але експорт повільніше.", "sprite_format_yaml_description": "Експорт як yaml-активи, які можна переглянути в редакторі. Це єдиний режим, який забезпечує точне відновлення всіх метаданих спрайтів.", "submesh_count": "Підра<PERSON><PERSON>нок субмешу", "swagger_documentation": "Документація Swagger", "target_version_for_version_changing": "Цільова версія для зміни версії", "terrain_format_heatmap": "Карта висот (Heightmap)", "text_asset_format_binary_description": "Експортувати необроблені дані текстового ресурсу як файл у форматі BYTES.", "warning_this_directory_is_not_empty_all_content_will_be_deleted": "Увага: ця директорія не порожня. Весь вміст буде видалено.", "asset_tab_development": "Розробка", "asset_tab_information": "Інформація", "assembly_name": "Назва Збірки", "asset_bundle_name": "Назва Asset Bundle", "asset_tab_hex": "Hex", "bundle": "Пакет (Bundle)", "main_asset": "Головні Асети", "asset_tab_model": "Модель", "audio_clip": "АудіоКліп", "class": "<PERSON><PERSON><PERSON><PERSON>", "assets": "Активи", "class_id_type_number": "Номер типу ідентифікатора класу", "class_name": "І<PERSON>'я Класу", "collection": "Колекція", "collections": "Колекції", "commands": "Команди", "menu_import": "Імпорт", "menu_view": "Вигляд", "mesh": "<PERSON><PERSON><PERSON> (Mesh)", "mesh_format_glb": "GLB", "namespace": "Простір імен(Namespace)", "no_data_has_been_loaded_for_this_key": "Немає даних для цього ключа.", "no_files_loaded": "Файли не завантажено", "not_implemented_yet": "Ще не реалізовано", "original_path": "Ориг<PERSON><PERSON><PERSON><PERSON>ний шлях", "path_id": "Ідентифіка<PERSON>ор шляху", "open_api_json": "ОpenAPI JSОN", "parent": "Батько", "privacy": "Конфіденційність", "remove": "Видалити", "replace": "Замінити", "resources": "Ресурси", "save": "Зберегти", "save_raw_data": "Зберегти необроблену інформацію", "save_settings_to_disk": "Зберегти параметри на диск", "scene": "Сцена", "script": "Скрипт", "script_content_level_0": "Рівень 0", "script_content_level_1": "Рівень 1", "there_are_no_keys_for_this_data_type": "Для цього типу даних немає ключів.", "transform": "Перетворення (Transform)", "vertex_count": "Кількість вершин", "export_primary_content": "Експорт основного контенту", "export_unity_project": "Експортувати проєкт Unity", "premium_feature_notice": "Преміум-функція", "create_subfolder": "Створити субпапки", "publicize_assemblies": "Оприлюднювати Assemblies", "remove_nullable_attributes": "Видалити нульові атрибути", "scripts_use_fully_qualified_type_names": "Скрипти використовують повні імена типів", "select_files": "Вибрати файли", "select_folders": "Вибрати папки"}