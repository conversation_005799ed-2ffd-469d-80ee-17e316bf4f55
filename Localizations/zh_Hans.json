{"asset_tab_audio": "音频（Audio）", "asset_tab_audio_duration_unknown": "<未知>", "asset_tab_audio_pause": "暂停", "asset_tab_audio_play": "播放", "asset_tab_basic": "基本信息", "asset_tab_image": "图像（Image）", "asset_tab_text": "文本（Text）", "asset_tab_yaml": "Yaml", "audio_export_title": "音频（Audio）导出格式", "audio_format_default": "默认", "audio_format_default_description": "将资源导出为嵌入在 FSB 中的内容类型。大多数音频类型都导出为 WAV，部分导出为 OGG。", "audio_format_force_wav": "转换成 WAV", "audio_format_force_wav_description": "将所有音频文件转换为 WAV 文件。 不建议在导入 Unity 时使用，以避免重复压缩造成的音质损失。", "audio_format_native": "原始格式（Raw）", "audio_format_native_description": "原始 FSB 音频。无法导入至 Unity，仅建议高级用户使用。", "audio_format_yaml": "Yaml", "audio_format_yaml_description": "导出为 Yaml 资源和 resS 文件。这是一项安全设置，出现问题时可以将其作为备份。", "bundled_assets_export_direct_export": "直接导出", "bundled_assets_export_direct_export_description": "捆绑资源（Bundled Assets）已在未分组的情况下导出。", "bundled_assets_export_group_by_asset_type": "按资源类型（Asset Type）分组", "bundled_assets_export_group_by_asset_type_description": "捆绑资源（Bundled Assets）的处理方式与来自其他文件中的资源（Assets）相同。", "bundled_assets_export_group_by_bundle_name": "按包名（Bundle Name）分组", "bundled_assets_export_group_by_bundle_name_description": "捆绑资源（Bundled Assets）已按其资源包名（Asset Bundle Name）分组。", "bundled_assets_export_title": "捆绑资源（Bundled Assets）导出模式", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "自动 - 实验性", "c_sharp_langage_version_config_auto_safe": "自动 - 安全", "c_sharp_langage_version_config_latest": "C# 最新版本", "c_sharp_language_version_config_description": "反编译脚本时所用的 C# 语言版本。", "check_log_for_more_details": "查看日志以获取更多信息", "config_options": "配置选项", "config_screen_drag_drop_prompt": "请注意，更改某些设置可能会导致或预防错误发生。\n准备好后，将你的游戏文件/文件夹拖放到此窗口中，或使用左上角的菜单手动打开它们。", "enable_prefab_outlining": "启用显示预制件（Prefab）轮廓", "enable_static_mesh_separation": "启用静态网格（Static Mesh）分离", "error": "错误", "error_exporting_with_reason": "导出游戏内容失败: {0}", "error_importing_with_reason": "载入游戏内容失败: {0}", "export_complete": "导出完成！", "export_deleting_old_files": "正在清理已有文件...", "export_in_progress": "正在导出资源文件\n{0} %\n{1} / {2}", "export_in_progress_no_file_count_yet": "正在导出资源文件\n0.0%\n?/?", "export_preparing": "正在准备导出...\n这可能需要几分钟。", "ignore_engine_assets": "忽略引擎资源（Engine Assets）", "image_export_title": "图片（Image）导出格式", "image_format_description": "这会对所有导出的图片（Image）生效。", "loading_game_content_from": "正在从 {0} 读取游戏内容\n{1}", "loading_step_begin_scheme_processing": "开始处理方案", "loading_step_create_file_collection": "正在创建文件合集", "loading_step_detect_platform": "正在收集文件并检测游戏架构", "loading_step_generate_dummy_dll": "正在从 IL2Cpp 生成 Mono 程序集", "loading_step_initialize_layout": "正在初始化资源布局", "loading_step_load_assemblies": "正在加载程序集", "loading_step_load_assets_from_file": "正在从 {0} 读取资源", "loading_step_locate_key_functions": "正在扫描库函数的 IL2Cpp 二进制文件", "loading_step_parse_archive": "正在解析 Archive 文件 {0}", "loading_step_parse_bundle": "正在解析 Bundle 资源 {0}", "loading_step_parse_il2cpp_metadata": "正在解析 IL2Cpp 元数据", "loading_step_parse_resource": "正在解析资源文件 {0}", "loading_step_parse_serialized": "正在解析已序列化的文件 {0}", "loading_step_parse_web": "正在解析 Web 文件 {0}", "loading_step_pre_processing": "正在预处理文件", "loading_step_restore_attributes": "正在恢复已生成程序集的属性", "loose_file_saved_at": "已将松散文件（Loose File）保存至 {0}", "menu_export": "导出", "menu_export_all": "导出所有文件", "menu_export_selected": "导出选中文件", "menu_export_selected_type": "导出所选类型的所有文件", "menu_file": "文件", "menu_file_exit": "退出程序", "menu_file_open_file": "打开文件", "menu_file_open_folder": "打开文件夹", "menu_file_reset": "重置", "menu_language": "语言", "mesh_export_title": "网格（Mesh）导出格式", "mesh_format_glb": "GLB", "mesh_format_glb_description": "FBX 的高品质开源替代格式。它是 GLTF 的二进制版本，只包含 Mesh 数据。可能会导致错误。 Unity 无法导入这种类型的资源。", "mesh_format_native": "Yaml", "mesh_format_native_description": "一种在 Unity 编辑器中使用网格（Mesh）的强大格式。可以通过各种各样的 Unity 包转换成其他格式。", "no_game_files_found": "在导入的文件中未找到 Unity 游戏或Asset Bundle。", "not_implemented_yet": "尚未实现", "script_content_level_0": "0级", "script_content_level_0_description": "脚本未导出。", "script_content_level_1": "1级", "script_content_level_1_description": "从反编译导出中移除方法（Strip Method）。", "script_content_level_2": "2级", "script_content_level_2_description": "默认。这会导出 Mono 游戏的完整方法和 IL2Cpp 游戏的虚拟方法。", "script_content_level_3": "3级", "script_content_level_3_description": "尽可能安全地恢复 IL2Cpp 方法。", "script_content_level_4": "4级", "script_content_level_4_description": "在不考虑安全性的情况下恢复 IL2Cpp 方法。", "script_content_level_title": "脚本（Script）内容级别", "script_export_format_decompiled": "反编译", "script_export_format_decompiled_description": "ILSpy 反编译器用于生成 C# 源码。 这是可靠的，但非常耗时并且可能会包含许多编译错误。", "script_export_format_dll_with_renaming": "导出重命名后的 DLL", "script_export_format_dll_without_renaming": "导出未重命名的 DLL", "script_export_format_dll_without_renaming_description": "程序集以编译的 DLL 格式导出。Unity 可能会覆写特殊的程序集（如 Assembly-CSharp）。", "script_export_format_hybrid": "混合", "script_export_title": "脚本（Script）导出格式", "script_language_version_title": "C# 语言版本", "select_asset_to_view_info": "从左侧选择资源以查看其相关信息", "shader_asset_export_title": "着色器（Shader）导出格式", "shader_asset_format_decompile": "反编译", "shader_asset_format_decompile_description": "将着色器（Shader）导出为 ShaderLab。非常实验性，几乎肯定会有错误。仅支持 DX11，不支持 DX9。", "shader_asset_format_disassembly": "反汇编", "shader_asset_format_disassembly_description": "将着色器（Shader）反汇编并导出。 此选项为实验性且容易损坏。这不会在 Unity Editor 中编译。", "shader_asset_format_dummy": "虚拟着色器（Dummy Shader）", "shader_asset_format_dummy_description": "将着色器（Shader）导出为虚拟着色器（Dummy Shader）。 尽管它保留了 Properties 和 FallBack 等数据，但它使用了通用且不透明的着色器（Shader）代码。", "shader_asset_format_yaml": "Yaml 资源", "shader_asset_format_yaml_description": "将着色器（Shader）导出为 Yaml 资源。实验性功能，仅可用于在 Unity Editor 中查看。Unity Editor 的预设行为可能会损坏这些文件。", "skip_streaming_assets": "跳过 SteamingAssets 目录", "sprite_export_title": "精灵（Sprite）导出格式", "sprite_format_native": "Unity", "sprite_format_native_description": "导出为 Unity 精灵（Sprite）格式。不能在 Unity 外查看。", "sprite_format_texture": "纹理（Texture）", "sprite_format_texture_description": "导出为 Sprite Sheet 格式的图片。 可以在 Unity 外查看，但导出速度更慢。", "sprite_format_yaml": "Yaml", "sprite_format_yaml_description": "导出成能够在文字编辑器中查看的 Yaml 资源格式。这是确保能精准恢复所有精灵（Sprites）元数据的唯一模式。", "success": "成功！", "terrain_export_title": "地形（Terrain）导出格式", "terrain_format_heatmap": "高度贴图（Heightmap）", "terrain_format_heatmap_description": "根据每个位置的地形 （Terrain）高度导出热图（Heatmap）。这只在您不关心细节或是拥有 3D 地形时才真正有用。", "terrain_format_mesh": "3D 网格（3D Mesh）", "terrain_format_mesh_description": "将地形（Terrain）导出为 GLB 格式的 3D 网格（3D Mesh），适用于使用各种 3D 编辑器查看。", "terrain_format_native": "Yaml", "terrain_format_native_description": "导出为原生的 Unity 地形（Terrain）格式。此选项最适用于会将其重新导入至 Unity 的情况。", "text_asset_export_title": "文本资源（TextAsset）导出格式", "text_asset_format_binary": "字节（Bytes）", "text_asset_format_binary_description": "将原始的文本资源（Text Asset）导出为二进制数据（不对 Text Asset 的实际格式进行猜测）。", "text_asset_format_parse": "解析（Parse）", "text_asset_format_parse_description": "导出为纯文本（Plain Text）文件，并尝试猜测其正确的文件扩展名（例如：JSON 文件会用 .json 扩展名）。", "text_asset_format_text": "纯文本（Plain Text）", "text_asset_format_text_description": "导出为纯文本文件（.txt）。", "welcome_title": "欢迎使用 AssetRipper", "donation_message": "如果您喜欢 AssetRipper，请考虑捐助我们：", "script": "脚本（<PERSON>rip<PERSON>）", "script_export_format_hybrid_description": "特殊的程序集（如 Assembly-CSharp）进行反编译。其他程序集以编译的 DLL 格式导出。", "audio_clip": "音频剪辑（AudioClip）", "bundle": "包（B<PERSON>le）", "bundles": "包（<PERSON><PERSON>les）", "asset_tab_information": "信息（Info）", "assets": "资源（Assets）", "asset_tab_development": "开发（Development）", "asset_tab_hex": "十六进制（Hex）", "assembly_name": "程序集名（Assembly Name）", "asset_bundle_name": "资源包名（Asset Bundle Name）", "default_version": "默认版本", "licenses": "许可", "csharp_type": "C# 类型", "game_object": "游戏对象（GameObject）", "format": "格式", "home": "主页", "height": "高度", "length": "长度", "json": "JSON", "data": "数据（Data）", "class": "类（Class）", "class_name": "类名（Class Name）", "count": "数量（Count）", "guid": "全局唯一标识符（GUID）", "mesh": "网格（Mesh）", "original_path": "源路径", "scene": "场景（Scene）", "privacy": "隐私（Privacy）", "path": "路径", "parent": "父项（Parent）", "resources": "资源（Resources）", "save": "保存", "no_files_loaded": "未加载任何文件", "namespace": "命名空间（Namespace）", "name": "名称（Name）", "main_asset": "主要资源（Main Asset）", "path_id": "路径 ID（Path ID）", "shader": "着色器（Shader）", "submesh_count": "子网格数（Submesh Count）", "texture": "纹理（Texture）", "welcome": "欢迎", "width": "宽度", "yaml": "Yaml", "vertex_count": "顶点数（Vertex Count）", "view_loaded_files": "查看已导入文件", "transform": "变换（Transform）", "collections": "合集", "collection": "合集", "frequency": "频率", "channels": "声道", "class_id_type_name": "类 ID 类型名（Class ID Type Name）", "class_id_type_number": "类 ID 类型号（Class ID Type Number）", "settings_can_only_be_changed_before_loading_files": "仅可在加载文件前修改设置。", "an_error_occured_during_decompilation": "反编译时出现错误。", "appreciation_message": "感谢您支持 AssetRipper！", "asset_tab_font": "字体（Font）", "asset_tab_dependencies": "依赖项（Dependencies）", "asset_ripper_free": "AssetRipper 自由分发版", "asset_ripper_premium": "AssetRipper 高级版", "configuration_files": "配置文件", "configuration_files_lists": "列表（List）", "configuration_files_singletons": "单项（Singletons）", "lightmap_texture_export_title": "光照贴图（Lightmap）导出格式", "lightmap_texture_format_description": "这会对所有导出的光照贴图（Lightmap）生效。", "load": "载入", "no_data_has_been_loaded_for_this_key": "未为此键加载任何数据。", "save_raw_data": "保存原始数据", "save_settings_to_disk": "将设置保存到本地", "settings": "设置", "target_version_for_version_changing": "要更改到的目标版本", "failed_files": "失败的文件", "commands": "命令", "asset_tab_model": "模型（Model）", "enable_asset_deduplication": "启用清除重复资源（Asset）", "experimental": "实验性", "menu_import": "导入", "menu_load": "载入", "menu_view": "查看", "open_api_json": "OpenAPI JSON 文档", "remove": "移除", "replace": "替换", "select_file": "选择文件", "select_folder": "选择文件夹", "size": "大小", "swagger_documentation": "Swagger 文档", "there_are_no_keys_for_this_data_type": "该数据类型并无任何键。", "warning_this_directory_is_not_empty_all_content_will_be_deleted": "警告：该目录非空，目录内的所有内容将会被删除。", "export_primary_content": "导出主要内容", "export_unity_project": "导出为 Unity 项目", "asset_tab_video": "视频（Video）", "stack_trace": "堆栈跟踪", "c_sharp_langage_version_config_12_0": "C# 12", "not_available_in_the_free_edition": "在自由分发版中不可用。", "override_path": "重载路径", "premium_feature_notice": "高级功能", "create_subfolder": "创建子文件夹", "select_folders": "选择多个文件夹", "publicize_assemblies": "公开化私有的程序集（Publicize Assemblies）", "scripts_use_fully_qualified_type_names": "在脚本中使用完整的（fully-qualified）类型名", "select_files": "选择多个文件", "remove_nullable_attributes": "移除可为空的属性（Attribute）"}