{"asset_tab_audio": "音訊", "asset_tab_audio_duration_unknown": "<長度不明>", "asset_tab_audio_pause": "暫停", "asset_tab_audio_play": "播放", "asset_tab_basic": "基本資訊", "asset_tab_image": "圖片", "asset_tab_text": "文字", "asset_tab_yaml": "Yaml", "audio_export_title": "音訊匯出格式", "audio_format_default": "預設", "audio_format_default_description": "將資源匯出為嵌入在 FSB 的內容類型。大部分音訊類型匯出為 WAV，部分匯出為 OGG。", "audio_format_force_wav": "轉換成 WAV", "audio_format_force_wav_description": "將所有音訊檔轉換為 WAV 檔案。不建議在匯入 Unity 時使用，以避免重新壓縮造成品質損失。", "audio_format_native": "原始 (Raw)", "audio_format_native_description": "未經處理的 FSB 音訊。無法匯入 Unity 中，所以只有進階使用者才可以使用。", "audio_format_yaml": "Yaml", "audio_format_yaml_description": "導出為 yaml 資產和 resS 文件。 這是一個安全的選擇，問題出現時也可以當備份。", "bundled_assets_export_direct_export": "直接匯出", "bundled_assets_export_direct_export_description": "Bundled assets在不分組的情況下導出。", "bundled_assets_export_group_by_asset_type": "按Asset分組", "bundled_assets_export_group_by_asset_type_description": "Bundled assets 的處理方式與其他文件中的assets相同。", "bundled_assets_export_group_by_bundle_name": "按Bundle名稱分組", "bundled_assets_export_group_by_bundle_name_description": "Bundled asset按其asset名稱分組。", "bundled_assets_export_title": "Bundled Assets 導出模式", "c_sharp_langage_version_config_1": "C# 1", "c_sharp_langage_version_config_10_0": "C# 10", "c_sharp_langage_version_config_11_0": "C# 11", "c_sharp_langage_version_config_2": "C# 2", "c_sharp_langage_version_config_3": "C# 3", "c_sharp_langage_version_config_4": "C# 4", "c_sharp_langage_version_config_5": "C# 5", "c_sharp_langage_version_config_6": "C# 6", "c_sharp_langage_version_config_7": "C# 7", "c_sharp_langage_version_config_7_1": "C# 7.1", "c_sharp_langage_version_config_7_2": "C# 7.2", "c_sharp_langage_version_config_7_3": "C# 7.3", "c_sharp_langage_version_config_8_0": "C# 8", "c_sharp_langage_version_config_9_0": "C# 9", "c_sharp_langage_version_config_auto_experimental": "自動 - 實驗性", "c_sharp_langage_version_config_auto_safe": "自動 - 安全", "c_sharp_langage_version_config_latest": "最新版本的 C#", "c_sharp_language_version_config_description": "反編譯時要使用的 C# 語言版本。", "check_log_for_more_details": "檢查紀錄以獲得更多細節", "config_options": "配置選項", "config_screen_drag_drop_prompt": "請注意，更改某些設定可能會導致或避開錯誤。\n準備就緒後，將遊戲檔案或資料夾拖曳到此視窗，或使用左上角的選單手動打開某些內容。", "enable_prefab_outlining": "啟用預設大綱", "enable_static_mesh_separation": "啟用靜態網格體分離", "error": "錯誤", "error_exporting_with_reason": "匯出此遊戲內容時發生錯誤： {0}", "error_importing_with_reason": "讀取此遊戲內容時發生錯誤： {0}", "export_complete": "匯出完成！", "export_deleting_old_files": "正在清除已經存在的檔案...", "export_in_progress": "正在匯出資源檔案\n{0} %\n{1} / {2}", "export_in_progress_no_file_count_yet": "正在匯出資源檔案\n0.0%\n?/?", "export_preparing": "正在準備匯出...\n這可能需要花費一些時間。", "image_export_title": "圖片匯出格式", "image_format_description": "影響所有匯出的圖片", "loading_game_content_from": "正在從 {0} 讀取遊戲內容\n{1}", "loading_step_begin_scheme_processing": "正在啟用 Scheme Processing", "loading_step_create_file_collection": "正在建立檔案叢集", "loading_step_detect_platform": "正在收集檔案並偵測遊戲架構", "loading_step_generate_dummy_dll": "從 IL2CPP 產生 Mono 架構的 Assembly 檔案", "loading_step_initialize_layout": "正在初始化資源架構", "loading_step_load_assemblies": "正在讀取 Assembly 檔案", "loading_step_load_assets_from_file": "正在從 {0} 讀取資源", "loading_step_locate_key_functions": "正在掃描 IL2CPP 函式庫", "loading_step_parse_archive": "正在解析 Archive 檔案 {0}", "loading_step_parse_bundle": "正在解析 Bundle 檔案 {0}", "loading_step_parse_il2cpp_metadata": "正在解析 IL2CPP Metadata", "loading_step_parse_resource": "正在解析資源檔案 {0}", "loading_step_parse_serialized": "正在解析已序列化檔案 {0}", "loading_step_parse_web": "正在解析 Web 檔案 {0}", "loading_step_pre_processing": "正在預處理檔案", "loading_step_restore_attributes": "正在恢復已產生的 Assembly 檔案的屬性", "loose_file_saved_at": "已將鬆散文件 (Loose File) 保存至 {0}", "menu_export": "匯出", "menu_export_all": "匯出所有檔案", "menu_export_selected": "匯出選取的檔案", "menu_export_selected_type": "匯出選取的檔案類型中的所有檔案", "menu_file": "檔案", "menu_file_exit": "退出 AssetRipper", "menu_file_open_file": "開啟檔案", "menu_file_open_folder": "開啟資料夾", "menu_file_reset": "重設", "menu_language": "語言", "mesh_export_title": "Mesh 匯出格式", "mesh_format_glb": "GLB", "mesh_format_glb_description": "FBX 的高品質開源替代格式。它是 GLTF 的二進制版本，只包含 Mesh 資料。可能會導致錯誤。Unity 無法匯入這種類型的資產。", "mesh_format_native": "Yaml", "mesh_format_native_description": "在編輯器中使用網格的強大格式。 可以通過各種unity包轉換成其他格式。", "no_game_files_found": "在拖入的檔案中找不到 Unity 遊戲或資源包。", "not_implemented_yet": "尚未實作", "script_content_level_0": "層級 0", "script_content_level_0_description": "不會匯出程式碼。", "script_content_level_1": "層級 1", "script_content_level_1_description": "透過反編譯匯出函式 (Method)。", "script_content_level_2": "層級 2", "script_content_level_2_description": "預設。這會匯出 Mono 架構的遊戲的完整函式 (Method) 或是 IL2Cpp 架構的遊戲的虛擬函式 (Method)。", "script_content_level_3": "層級 3", "script_content_level_3_description": "盡可能安全地恢復 IL2Cpp 的函式 (Method)。", "script_content_level_4": "層級 4", "script_content_level_4_description": "盡可能地恢復 IL2Cpp 的函式 (Method)，但不保證安全性。", "script_content_level_title": "指令碼內容層級", "script_export_format_decompiled": "反編譯", "script_export_format_decompiled_description": "ILSpy 反編譯器用於產生 C# 程式碼。這是可靠的。而代價是它非常耗時且包含許多編譯錯誤。", "script_export_format_dll_with_renaming": "匯出 DLL 並重新命名", "script_export_format_dll_without_renaming": "匯出 DLL 而沒有重新命名", "script_export_format_dll_without_renaming_description": "Assembly 檔案以其已編譯的 Dll 格式匯出。這個選項是實驗性的。可能毫無作用。", "script_export_format_hybrid": "Hybrid", "script_export_title": "指令碼匯出格式", "script_language_version_title": "C# 語言版本", "select_asset_to_view_info": "在左側選擇資源以查看它的相關資訊", "shader_asset_export_title": "Shader 匯出格式", "shader_asset_format_decompile": "反編譯", "shader_asset_format_decompile_description": "將 Shader 匯出為 ShaderLab。此選項非常實驗性，幾乎肯定會有錯誤。僅支持 DX11，不支持 DX9。", "shader_asset_format_disassembly": "反組譯", "shader_asset_format_disassembly_description": "將 Shader 反組譯後匯出。此選項為實驗性且容易破損。這不會在編輯器中編譯。", "shader_asset_format_dummy": "虛擬 Shader", "shader_asset_format_dummy_description": "將 Shader 匯出為虛擬 Shader。 儘管它保留了 Properties 和 FallBack 等資料，但它使用了通用且不透明的 Shader code。", "shader_asset_format_yaml": "Yaml 資源", "shader_asset_format_yaml_description": "將 Shader 匯出為 Yaml 資產。此選項為實驗性。僅可用於在編輯器中查看。編輯器的預設行為可能損壞這些文件。", "skip_streaming_assets": "跳過 StreamingAssets 目錄", "sprite_export_title": "Sprite 匯出格式", "sprite_format_native": "Unity", "sprite_format_native_description": "匯出為 Unity Sprite 格式。不能在 Unity 外檢視。", "sprite_format_texture": "紋理 (Texture)", "sprite_format_texture_description": "匯出成 Sprite Sheet 格式的圖片。可以在 Unity 外檢視，但匯出速度更慢。", "sprite_format_yaml": "Yaml", "sprite_format_yaml_description": "匯出成能夠在文字編輯器中查看的 Yaml 資產格式。這是確保能精準恢復所有 Sprites Metadata 的唯一模式。", "success": "成功！", "terrain_export_title": "Terrain 匯出格式", "terrain_format_heatmap": "高度圖 (Heightmap)", "terrain_format_heatmap_description": "根據每個位置的地形 (Terrain) 高度匯出熱點圖 (Heatmap)。這只在您不關心細節或是擁有 3D 地形時才真正有用。", "terrain_format_mesh": "3D Mesh", "terrain_format_mesh_description": "將 Terrain 匯出成 GLB 格式的 3D Mesh，適合使用各種 3D 編輯器查看。", "terrain_format_native": "Yaml", "terrain_format_native_description": "匯出為原生的 Unity Terrain 格式。如果您計劃重新匯入 Unity，此選項最適合使用。", "text_asset_export_title": "TextAsset 匯出格式", "text_asset_format_binary": "Bytes", "text_asset_format_binary_description": "將文字資產的原始資料 (Raw Bytes) 以副檔名 .bytes 的方式直接匯出 (不會猜測其文字資產的實際格式)。", "text_asset_format_parse": "剖析 (Parse)", "text_asset_format_parse_description": "匯出為純文字檔案，並嘗試猜測正確的檔案格式。 (例：JSON 格式檔案將會以 .json 作為副檔名)", "text_asset_format_text": "純文字", "text_asset_format_text_description": "匯出為純文字檔案 (.txt)", "welcome_title": "歡迎來到 AssetRipper"}