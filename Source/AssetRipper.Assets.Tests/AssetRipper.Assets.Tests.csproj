<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<IsPackable>false</IsPackable>
		<OutputPath>..\0Bins\Other\AssetRipper.Assets.Tests\$(Configuration)\</OutputPath>
		<IntermediateOutputPath>..\0Bins\obj\AssetRipper.Assets.Tests\$(Configuration)\</IntermediateOutputPath>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.0" />
		<PackageReference Include="NUnit" Version="4.3.2" />
		<PackageReference Include="NUnit3TestAdapter" Version="5.0.0" />
		<PackageReference Include="NUnit.Analyzers" Version="4.8.1">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\AssetRipper.Assets\AssetRipper.Assets.csproj" />
		<ProjectReference Include="..\AssetRipper.SourceGenerated.Extensions\AssetRipper.SourceGenerated.Extensions.csproj" />
	</ItemGroup>

</Project>
