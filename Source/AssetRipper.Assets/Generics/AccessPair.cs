﻿namespace AssetRipper.Assets.Generics
{
	public sealed class AccessPair<TK<PERSON>, <PERSON><PERSON>ue, T<PERSON>eyBase, TValueBase> : AccessPairBase<TKeyBase, TValueBase>
		where TKeyBase : notnull
		where TValueBase : notnull
		where TK<PERSON> : notnull, T<PERSON><PERSON>Base, new()
		where TValue : notnull, TValueBase, new()
	{
		private readonly AccessPairBase<TKey, TValue> referencePair;

		public AccessPair(AccessPairBase<TKey, TValue> pair)
		{
			referencePair = pair;
		}

		public override TKeyBase Key
		{
			get => referencePair.Key;
			set => referencePair.Key = (TKey)value;
		}

		public override TValueBase Value
		{
			get => referencePair.Value;
			set => referencePair.Value = (TValue)value;
		}
	}
}
