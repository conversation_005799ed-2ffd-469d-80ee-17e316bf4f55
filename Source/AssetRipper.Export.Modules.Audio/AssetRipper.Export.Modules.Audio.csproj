﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<IsTrimmable>true</IsTrimmable>
		<OutputPath>..\0Bins\Other\AssetRipper.Export.Modules.Audio\$(Configuration)\</OutputPath>
		<IntermediateOutputPath>..\0Bins\obj\AssetRipper.Export.Modules.Audio\$(Configuration)\</IntermediateOutputPath>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Fmod5Sharp" Version="3.0.1" />
		<PackageReference Include="NAudio.Core" Version="2.2.1" />
		<PackageReference Include="NAudio.Vorbis" Version="1.5.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\AssetRipper.Export\AssetRipper.Export.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="C:\Users\<USER>\.nuget\packages\naudio.vorbis\**\README.md" />
	</ItemGroup>

</Project>
