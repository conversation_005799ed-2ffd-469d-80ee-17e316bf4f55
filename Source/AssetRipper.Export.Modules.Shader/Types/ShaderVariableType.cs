﻿namespace AssetRipper.Export.Modules.Shaders.Types
{
	internal enum ShaderVariableType
	{
		Void = 0,
		Bool = 1,
		Int = 2,
		Float = 3,
		String = 4,
		Texture = 5,
		Texture1D = 6,
		Texture2D = 7,
		Texture3D = 8,
		TextureCube = 9,
		Sam<PERSON> = 10,
		PixelShader = 15,
		VertexShader = 16,
		UInt = 19,
		UInt8 = 20,
		GeometryShader = 21,
		Rasterizer = 22,
		DepthStencil = 23,
		Blend = 24,
		<PERSON>uffer = 25,
		CBuffer = 26,
		TBuffer = 27,
		Texture1DArray = 28,
		Texture2DArray = 29,
		RenderTargetView = 30,
		DepthStencilView = 31,
		Texture2DMultiSampled = 32,
		Texture2DMultiSampledArray = 33,
		TextureCubeArray = 34,
		// The following are new in D3D11.
		HullShader = 35,
		DomainShader = 36,
		InterfacePointer = 37,
		ComputeShader = 38,
		Double = 39,
		ReadWriteTexture1D,
		ReadWriteTexture1DArray,
		ReadWriteTexture2D,
		Read<PERSON>riteTexture2DArray,
		Read<PERSON>riteTexture3D,
		<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
		<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uffer,
		ReadWriteByteAddressBuffer,
		StructuredBuffer,
		ReadWriteStructuredBuffer,
		AppendStructuredBuffer,
		ConsumeStructuredBuffer
	}
}
