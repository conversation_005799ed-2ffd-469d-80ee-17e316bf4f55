﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<IsTrimmable>true</IsTrimmable>
		<OutputPath>..\0Bins\Other\AssetRipper.Export.Modules.Textures\$(Configuration)\</OutputPath>
		<IntermediateOutputPath>..\0Bins\obj\AssetRipper.Export.Modules.Textures\$(Configuration)\</IntermediateOutputPath>
		<AllowUnsafeBlocks>true</AllowUnsafeBlocks>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\AssetRipper.Export\AssetRipper.Export.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AssetRipper.TextureDecoder" Version="2.3.0" />
		<PackageReference Include="AssetRipper.Tpk" Version="1.1.0" />
		<PackageReference Include="Kyaru.Texture2DDecoder" Version="0.17.0" />
		<PackageReference Include="Kyaru.Texture2DDecoder.Windows" Version="0.1.0" />
		<PackageReference Include="Kyaru.Texture2DDecoder.macOS" Version="0.1.0" />
		<PackageReference Include="Kyaru.Texture2DDecoder.Linux" Version="0.1.0" />
		<PackageReference Include="StbImageWriteSharp" Version="1.16.7" />
	</ItemGroup>

	<ItemGroup>
		<ContentWithTargetPath Include="Libraries\x64\crunch_x64.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<TargetPath>crunch.dll</TargetPath>
		</ContentWithTargetPath>
		<ContentWithTargetPath Include="Libraries\x64\crunchunity_x64.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<TargetPath>crunchunity.dll</TargetPath>
		</ContentWithTargetPath>
	</ItemGroup>

</Project>
