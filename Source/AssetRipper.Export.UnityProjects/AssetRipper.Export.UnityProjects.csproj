﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<AllowUnsafeBlocks>true</AllowUnsafeBlocks>
		<IsTrimmable>true</IsTrimmable>
		<OutputPath>..\0Bins\Other\AssetRipper.Export.UnityProjects\$(Configuration)\</OutputPath>
		<IntermediateOutputPath>..\0Bins\obj\AssetRipper.Export.UnityProjects\$(Configuration)\</IntermediateOutputPath>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AssetRipper.HashAlgorithms" Version="1.0.0" />
		<PackageReference Include="AssetRipper.ICSharpCode.Decompiler" Version="9.1.0.8002" />
		<PackageReference Include="AssetRipper.Mining.PredefinedAssets" Version="1.5.0" />
		<PackageReference Include="AssetRipper.Tpk" Version="1.1.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\AssetRipper.Export.Modules.Audio\AssetRipper.Export.Modules.Audio.csproj" />
		<ProjectReference Include="..\AssetRipper.Export.Modules.Models\AssetRipper.Export.Modules.Models.csproj" />
		<ProjectReference Include="..\AssetRipper.Export.Modules.Shader\AssetRipper.Export.Modules.Shaders.csproj" />
		<ProjectReference Include="..\AssetRipper.Export.Modules.Textures\AssetRipper.Export.Modules.Textures.csproj" />
		<ProjectReference Include="..\AssetRipper.Import\AssetRipper.Import.csproj" />
		<ProjectReference Include="..\AssetRipper.Processing\AssetRipper.Processing.csproj" />
		<ProjectReference Include="..\AssetRipper.Yaml\AssetRipper.Yaml.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="Shaders\Templates\*" />
		<EmbeddedResource Include="Shaders\Templates\*" />
	</ItemGroup>

	<ItemGroup>
		<Content Remove="C:\Users\<USER>\.nuget\packages\icsharpcode.decompiler\**\Humanizer\LICENSE" />
		<Content Remove="C:\Users\<USER>\.nuget\packages\icsharpcode.decompiler\**\Pattern Matching.html" />
	</ItemGroup>

</Project>
