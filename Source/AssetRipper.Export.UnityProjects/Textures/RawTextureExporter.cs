﻿using AssetRipper.Assets;
using AssetRipper.SourceGenerated.Classes.ClassID_28;
using AssetRipper.SourceGenerated.Extensions;

namespace AssetRipper.Export.UnityProjects.Textures
{
	internal class RawTextureExporter : BinaryAssetExporter
	{
		public override bool TryCreateCollection(IUnityObjectBase asset, [NotNullWhen(true)] out IExportCollection? exportCollection)
		{
			if (asset is ITexture2D texture)
			{
				exportCollection = new RawTextureExportCollection(this, texture);
				return true;
			}
			else
			{
				exportCollection = null;
				return false;
			}
		}

		public override bool Export(IExportContainer container, IUnityObjectBase asset, string path, FileSystem fileSystem)
		{
			fileSystem.File.WriteAllBytes(path, ((ITexture2D)asset).GetImageData());
			return true;
		}
	}
}
