﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<ServerGarbageCollection>true</ServerGarbageCollection>
		<OptimizationPreference>Size</OptimizationPreference>
		<PublishAot>true</PublishAot>
		<TrimmerSingleWarn>false</TrimmerSingleWarn>
		<OutputPath>..\0Bins\AssetRipper.GUI.Free\$(Configuration)\</OutputPath>
		<IntermediateOutputPath>..\0Bins\obj\AssetRipper.GUI.Free\$(Configuration)\</IntermediateOutputPath>
	</PropertyGroup>

	<PropertyGroup>
		<!-- https://learn.microsoft.com/en-us/dotnet/core/deploying/trimming/trimming-options#trim-framework-library-features -->
		<EnableUnsafeBinaryFormatterSerialization>false</EnableUnsafeBinaryFormatterSerialization>
		<EnableUnsafeUTF7Encoding>false</EnableUnsafeUTF7Encoding>
		<EventSourceSupport>false</EventSourceSupport>
		<InvariantGlobalization>true</InvariantGlobalization>
		<MetadataUpdaterSupport>false</MetadataUpdaterSupport>
		<MetricsSupport>false</MetricsSupport>
		<XmlResolverIsNetworkingEnabledByDefault>false</XmlResolverIsNetworkingEnabledByDefault>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)' == 'Release'">
		<DebuggerSupport>false</DebuggerSupport>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\AssetRipper.GUI.Web\AssetRipper.GUI.Web.csproj" />
	</ItemGroup>

</Project>
