﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>netstandard2.0</TargetFramework>
		<IsAotCompatible>false</IsAotCompatible>
		<AppendTargetFrameworkToOutputPath>true</AppendTargetFrameworkToOutputPath>
		<EnforceExtendedAnalyzerRules>true</EnforceExtendedAnalyzerRules>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.CodeAnalysis.Analyzers" Version="4.14.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.14.0" />
		<PackageReference Include="SourceGenerator.Foundations" Version="2.0.13" />
	</ItemGroup>

	<ItemGroup>
		<!-- Generator dependencies -->
		<PackageReference Include="AssetRipper.Text.SourceGeneration" Version="1.2.2" PrivateAssets="all" GeneratePathProperty="true" />
	</ItemGroup>

</Project>
