<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>exe</OutputType>
		<OutputPath>..\0Bins\Other\AssetRipper.GUI.SourceGenerator\$(Configuration)\</OutputPath>
		<IntermediateOutputPath>..\0Bins\obj\AssetRipper.GUI.SourceGenerator\$(Configuration)\</IntermediateOutputPath>
		<IsAotCompatible>false</IsAotCompatible>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AssetRipper.Text.SourceGeneration" Version="1.2.2" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\AssetRipper.Export.UnityProjects\AssetRipper.Export.UnityProjects.csproj" />
	</ItemGroup>

</Project>