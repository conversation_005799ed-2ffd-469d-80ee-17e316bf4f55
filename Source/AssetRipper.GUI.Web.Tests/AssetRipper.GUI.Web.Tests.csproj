<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<IsPackable>false</IsPackable>
		<OutputPath>..\0Bins\Other\AssetRipper.GUI.Web.Tests\$(Configuration)\</OutputPath>
		<IntermediateOutputPath>..\0Bins\obj\AssetRipper.GUI.Web.Tests\$(Configuration)\</IntermediateOutputPath>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.0" />
		<PackageReference Include="NUnit" Version="4.3.2" />
		<PackageReference Include="NUnit3TestAdapter" Version="5.0.0" />
		<PackageReference Include="NUnit.Analyzers" Version="4.8.1">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\AssetRipper.GUI.Web\AssetRipper.GUI.Web.csproj" />
	</ItemGroup>

</Project>
