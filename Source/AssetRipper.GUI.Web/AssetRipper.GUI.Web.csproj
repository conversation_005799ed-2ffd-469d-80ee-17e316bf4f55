<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<ImplicitUsings>enable</ImplicitUsings>
		<OutputPath>..\0Bins\Other\AssetRipper.GUI.Web\$(Configuration)\</OutputPath>
		<IntermediateOutputPath>..\0Bins\obj\AssetRipper.GUI.Web\$(Configuration)\</IntermediateOutputPath>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\AssetRipper.Export.PrimaryContent\AssetRipper.Export.PrimaryContent.csproj" />
		<ProjectReference Include="..\AssetRipper.Export.UnityProjects\AssetRipper.Export.UnityProjects.csproj" />
		<ProjectReference Include="..\AssetRipper.GUI.Licensing\AssetRipper.GUI.Licensing.csproj" />
		<ProjectReference Include="..\AssetRipper.GUI.Localizations\AssetRipper.GUI.Localizations.csproj" />
		<ProjectReference Include="..\AssetRipper.Web\AssetRipper.Web.csproj" />
	</ItemGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="StaticContent/css/site.css" />
		<EmbeddedResource Include="StaticContent/favicon.ico" />
		<EmbeddedResource Include="StaticContent/js/site.js" />
		<EmbeddedResource Include="StaticContent/js/commands_page.js" />
		<EmbeddedResource Include="StaticContent/js/mesh_preview.js" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AssetRipper.GUI.Web.Dependencies" Version="1.0.0" />
		<PackageReference Include="AssetRipper.Text.Html" Version="2.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
		<PackageReference Include="Microsoft.OpenApi" Version="1.6.24" />
		<PackageReference Include="oqo0.SwaggerThemes" Version="1.4.3" />
		<PackageReference Include="Photino.NET" Version="4.0.16" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2" />
		<PackageReference Include="System.CommandLine" Version="2.0.0-beta4.22272.1" />
	</ItemGroup>

</Project>
