html {
	font-size: 14px;
}

@media (min-width: 768px) {
	html {
		font-size: 16px;
	}
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
	box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
	position: relative;
	min-height: 100%;
}

body {
	margin: 0;
	padding: 0;
	min-height: 100vh; /* Ensure the body takes up at least the full height of the viewport */
	display: flex;
	flex-direction: column;
}

main {
	flex: 1; /* Allow the main content to grow and fill available space */
}

footer {
	padding: 10px;
	margin-top: auto; /* Push the footer to the bottom of the container */
}

/* This lets the dropdown descriptions in the settings menu render correctly. */
.dropdown-description.disabled {
	display: none; /* Hide disabled elements */
}