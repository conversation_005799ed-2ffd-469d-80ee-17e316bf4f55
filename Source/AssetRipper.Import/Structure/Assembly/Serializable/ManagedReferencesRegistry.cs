using AssetRipper.Assets;
using AssetRipper.Assets.Cloning;
using AssetRipper.Assets.IO.Writing;
using AssetRipper.Assets.Traversal;
using AssetRipper.Import.Logging;
using AssetRipper.Import.Structure.Assembly.Managers;
using AssetRipper.IO.Endian;
using AssetRipper.IO.Files.SerializedFiles;
using AssetRipper.SerializationLogic;
using System.Collections.Generic;

namespace AssetRipper.Import.Structure.Assembly.Serializable;

public sealed class ManagedReferencesRegistry : UnityAssetBase, IDeepCloneable
{
	public sealed class ManagedReferencedObject
	{
		public long Rid { get; set; }
		public ManagedTypeReference Type { get; set; } = new();
		public SerializableStructure? Data { get; set; }
	}

	public sealed class ManagedTypeReference
	{
		public string ClassName { get; set; } = string.Empty;
		public string Namespace { get; set; } = string.Empty;
		public string Assembly { get; set; } = string.Empty;

		public bool IsNull => string.IsNullOrEmpty(ClassName) && string.IsNullOrEmpty(Namespace) && string.IsNullOrEmpty(Assembly);
	}

	public int Version { get; set; }
	public List<ManagedReferencedObject> References { get; } = new();

	// Store version and flags for managed reference data reading
	private UnityVersion currentVersion = UnityVersion.MinVersion;
	private TransferInstructionFlags currentFlags = TransferInstructionFlags.NoTransferInstructionFlags;

	// Assembly manager for type resolution
	private IAssemblyManager? assemblyManager;

	public override void ReadEditor(ref EndianSpanReader reader)
	{
		Read(ref reader, currentVersion, currentFlags);
	}

	public override void ReadRelease(ref EndianSpanReader reader)
	{
		Read(ref reader, currentVersion, currentFlags);
	}

	public void Read(ref EndianSpanReader reader, UnityVersion version, TransferInstructionFlags flags)
	{
		currentVersion = version;
		currentFlags = flags;
		Read(ref reader);
	}

	public void Read(ref EndianSpanReader reader, UnityVersion version, TransferInstructionFlags flags, IAssemblyManager assemblyManager)
	{
		currentVersion = version;
		currentFlags = flags;
		this.assemblyManager = assemblyManager;
		Read(ref reader);
	}

	private void Read(ref EndianSpanReader reader)
	{
		Version = reader.ReadInt32();
		References.Clear();

		if (Version == 1)
		{
			// Version 1 uses a null-terminated list
			while (true)
			{
				var refObject = new ManagedReferencedObject
				{
					Rid = References.Count // Version 1 uses consecutive rids
				};

				refObject.Type.ClassName = reader.ReadUtf8StringAligned().String;
				refObject.Type.Namespace = reader.ReadUtf8StringAligned().String;
				refObject.Type.Assembly = reader.ReadUtf8StringAligned().String;

				if (refObject.Type.IsNull)
				{
					break; // TERMINUS reached
				}

				// Read the actual data structure based on the type
				refObject.Data = ReadManagedReferenceData(ref reader, refObject.Type);
				References.Add(refObject);
			}
		}
		else if (Version == 2)
		{
			int count = reader.ReadInt32();
			for (int i = 0; i < count; i++)
			{
				var refObject = new ManagedReferencedObject
				{
					Rid = reader.ReadInt64()
				};

				refObject.Type.ClassName = reader.ReadUtf8StringAligned().String;
				refObject.Type.Namespace = reader.ReadUtf8StringAligned().String;
				refObject.Type.Assembly = reader.ReadUtf8StringAligned().String;

				if (!refObject.Type.IsNull)
				{
					// Read the actual data structure based on the type
					// The data is serialized as a SerializableStructure
					refObject.Data = ReadManagedReferenceData(ref reader, refObject.Type);
				}

				References.Add(refObject);
			}
			reader.Align();
		}
		else
		{
			throw new NotSupportedException($"ManagedReferencesRegistry version {Version} is not supported.");
		}
	}

	private SerializableStructure? ReadManagedReferenceData(ref EndianSpanReader reader, ManagedTypeReference type)
	{
		if (type.IsNull)
		{
			return null;
		}

		// Try to resolve the type using the assembly manager
		SerializableType? serializableType = ResolveSerializableType(type);
		if (serializableType == null)
		{
			// If we can't resolve the type, we have a problem because we don't know how much data to skip
			// Log a warning and return null - the caller will need to handle this
			Logger.Warning(LogCategory.Import, $"Could not resolve managed reference type: {type.Namespace}.{type.ClassName} from assembly {type.Assembly}");
			return null;
		}

		// Create and read the structure
		var structure = new SerializableStructure(serializableType, 0);

		try
		{
			// Read with the current Unity version and flags
			structure.Read(ref reader, currentVersion, currentFlags);
			return structure;
		}
		catch (Exception ex)
		{
			// If reading fails, log the error and return null
			Logger.Warning(LogCategory.Import, $"Failed to read managed reference data for type {type.Namespace}.{type.ClassName}: {ex.Message}");
			return null;
		}
	}

	private SerializableType? ResolveSerializableType(ManagedTypeReference type)
	{
		if (assemblyManager == null)
		{
			return null;
		}

		try
		{
			// Try to get the type from the assembly manager
			string fullTypeName = string.IsNullOrEmpty(type.Namespace)
				? type.ClassName
				: $"{type.Namespace}.{type.ClassName}";

			if (assemblyManager.TryGetSerializableType(type.Assembly, fullTypeName, out SerializableType? serializableType, out _))
			{
				return serializableType;
			}
		}
		catch (Exception)
		{
			// Ignore exceptions during type resolution
		}

		return null;
	}





	public override void WriteEditor(AssetWriter writer)
	{
		Write(writer);
	}

	public override void WriteRelease(AssetWriter writer)
	{
		Write(writer);
	}

	private void Write(AssetWriter writer)
	{
		writer.Write(Version);

		if (Version == 1)
		{
			foreach (var reference in References)
			{
				writer.Write(reference.Type.ClassName);
				writer.AlignStream();
				writer.Write(reference.Type.Namespace);
				writer.AlignStream();
				writer.Write(reference.Type.Assembly);
				writer.AlignStream();

				reference.Data?.WriteRelease(writer);
			}
			// Write TERMINUS
			writer.Write(string.Empty);
			writer.AlignStream();
			writer.Write(string.Empty);
			writer.AlignStream();
			writer.Write(string.Empty);
			writer.AlignStream();
		}
		else if (Version == 2)
		{
			writer.Write(References.Count);
			foreach (var reference in References)
			{
				writer.Write(reference.Rid);
				writer.Write(reference.Type.ClassName);
				writer.AlignStream();
				writer.Write(reference.Type.Namespace);
				writer.AlignStream();
				writer.Write(reference.Type.Assembly);
				writer.AlignStream();

				reference.Data?.WriteRelease(writer);
			}
			writer.AlignStream();
		}
	}

	public override void WalkEditor(AssetWalker walker)
	{
		// Walk the managed references registry
		if (walker.EnterAsset(this))
		{
			if (walker.EnterField(this, nameof(Version)))
			{
				walker.VisitPrimitive(Version);
				walker.ExitField(this, nameof(Version));
			}

			walker.DivideAsset(this);

			if (walker.EnterField(this, "RefIds"))
			{
				if (walker.EnterList(References))
				{
					for (int i = 0; i < References.Count; i++)
					{
						if (i > 0)
						{
							walker.DivideList(References);
						}

						var reference = References[i];
						// TODO: Walk the reference object
					}
					walker.ExitList(References);
				}
				walker.ExitField(this, "RefIds");
			}

			walker.ExitAsset(this);
		}
	}

	public override void Reset()
	{
		Version = 0;
		References.Clear();
	}

	public IUnityAssetBase DeepClone(PPtrConverter converter)
	{
		var clone = new ManagedReferencesRegistry
		{
			Version = Version
		};

		foreach (var reference in References)
		{
			var clonedRef = new ManagedReferencedObject
			{
				Rid = reference.Rid,
				Type = new ManagedTypeReference
				{
					ClassName = reference.Type.ClassName,
					Namespace = reference.Type.Namespace,
					Assembly = reference.Type.Assembly
				},
				Data = reference.Data?.DeepClone(converter)
			};
			clone.References.Add(clonedRef);
		}

		return clone;
	}
} 