using AssetRipper.Assets;
using AssetRipper.Assets.Cloning;
using AssetRipper.Assets.IO.Writing;
using AssetRipper.Assets.Traversal;
using AssetRipper.Import.Structure.Assembly.TypeTrees;
using AssetRipper.IO.Endian;
using AssetRipper.IO.Files;
using AssetRipper.IO.Files.SerializedFiles;
using AssetRipper.SerializationLogic;
using System.Collections.Generic;

namespace AssetRipper.Import.Structure.Assembly.Serializable;

public sealed class ManagedReferencesRegistry : UnityAssetBase, IDeepCloneable
{
	public sealed class ManagedReferencedObject
	{
		public long Rid { get; set; }
		public ManagedTypeReference Type { get; set; } = new();
		public SerializableStructure? Data { get; set; }
	}

	public sealed class ManagedTypeReference
	{
		public string ClassName { get; set; } = string.Empty;
		public string Namespace { get; set; } = string.Empty;
		public string Assembly { get; set; } = string.Empty;

		public bool IsNull => string.IsNullOrEmpty(ClassName) && string.IsNullOrEmpty(Namespace) && string.IsNullOrEmpty(Assembly);
	}

	public int Version { get; set; }
	public List<ManagedReferencedObject> References { get; } = new();

	// Store version and flags for managed reference data reading
	private UnityVersion currentVersion = UnityVersion.MinVersion;
	private TransferInstructionFlags currentFlags = TransferInstructionFlags.NoTransferInstructionFlags;

	public override void ReadEditor(ref EndianSpanReader reader)
	{
		Read(ref reader, currentVersion, currentFlags);
	}

	public override void ReadRelease(ref EndianSpanReader reader)
	{
		Read(ref reader, currentVersion, currentFlags);
	}

	public void Read(ref EndianSpanReader reader, UnityVersion version, TransferInstructionFlags flags)
	{
		currentVersion = version;
		currentFlags = flags;
		Read(ref reader);
	}

	private void Read(ref EndianSpanReader reader)
	{
		Version = reader.ReadInt32();
		References.Clear();

		if (Version == 1)
		{
			// Version 1 uses a null-terminated list
			while (true)
			{
				var refObject = new ManagedReferencedObject
				{
					Rid = References.Count // Version 1 uses consecutive rids
				};

				refObject.Type.ClassName = reader.ReadUtf8StringAligned().String;
				refObject.Type.Namespace = reader.ReadUtf8StringAligned().String;
				refObject.Type.Assembly = reader.ReadUtf8StringAligned().String;

				if (refObject.Type.IsNull)
				{
					break; // TERMINUS reached
				}

				// Read the actual data structure based on the type
				refObject.Data = ReadManagedReferenceData(ref reader, refObject.Type);
				References.Add(refObject);
			}
		}
		else if (Version == 2)
		{
			int count = reader.ReadInt32();
			for (int i = 0; i < count; i++)
			{
				var refObject = new ManagedReferencedObject
				{
					Rid = reader.ReadInt64()
				};

				refObject.Type.ClassName = reader.ReadUtf8StringAligned().String;
				refObject.Type.Namespace = reader.ReadUtf8StringAligned().String;
				refObject.Type.Assembly = reader.ReadUtf8StringAligned().String;

				if (!refObject.Type.IsNull)
				{
					// Read the actual data structure based on the type
					// The data is serialized as a SerializableStructure
					refObject.Data = ReadManagedReferenceData(ref reader, refObject.Type);
				}

				References.Add(refObject);
			}
			reader.Align();
		}
		else
		{
			throw new NotSupportedException($"ManagedReferencesRegistry version {Version} is not supported.");
		}
	}

	private SerializableStructure? ReadManagedReferenceData(ref EndianSpanReader reader, ManagedTypeReference type)
	{
		if (type.IsNull)
		{
			return null;
		}

		// Read the embedded type tree for this managed reference
		// The format is similar to a regular type tree but embedded in the data
		var rootNode = ReadEmbeddedTypeTree(ref reader);
		if (rootNode == null)
		{
			return null;
		}

		// Create a SerializableType from the type tree
		var serializableType = SerializableTreeType.FromRootNode(rootNode.Value, false);
		
		// Create and read the structure
		var structure = new SerializableStructure(serializableType, 0);
		
		// Read with the current Unity version and flags
		structure.Read(ref reader, currentVersion, currentFlags);
		
		return structure;
	}

	private TypeTreeNodeStruct? ReadEmbeddedTypeTree(ref EndianSpanReader reader)
	{
		// Read the embedded type tree format
		// This is a simplified version - the actual format may be more complex
		
		int nodeCount = reader.ReadInt32();
		if (nodeCount == 0)
		{
			return null;
		}

		int stringBufferSize = reader.ReadInt32();
		
		// Read nodes
		var nodes = new List<EmbeddedNode>(nodeCount);
		for (int i = 0; i < nodeCount; i++)
		{
			var node = new EmbeddedNode
			{
				Version = reader.ReadUInt16(),
				Level = reader.ReadByte(),
				TypeFlags = reader.ReadByte(),
				TypeStrOffset = reader.ReadUInt32(),
				NameStrOffset = reader.ReadUInt32(),
				ByteSize = reader.ReadInt32(),
				Index = reader.ReadInt32(),
				MetaFlag = reader.ReadUInt32()
			};
			nodes.Add(node);
		}

		// Read string buffer
		byte[] stringBuffer = reader.ReadBytes(stringBufferSize);
		
		// Convert to TypeTreeNodeStruct
		return ConvertToTypeTreeNode(nodes, stringBuffer, 0);
	}

	private struct EmbeddedNode
	{
		public ushort Version;
		public byte Level;
		public byte TypeFlags;
		public uint TypeStrOffset;
		public uint NameStrOffset;
		public int ByteSize;
		public int Index;
		public uint MetaFlag;
	}

	private TypeTreeNodeStruct? ConvertToTypeTreeNode(List<EmbeddedNode> nodes, byte[] stringBuffer, int nodeIndex)
	{
		if (nodeIndex >= nodes.Count)
		{
			return null;
		}

		var node = nodes[nodeIndex];
		string typeName = ReadString(stringBuffer, (int)node.TypeStrOffset);
		string name = ReadString(stringBuffer, (int)node.NameStrOffset);
		
		// Find child nodes
		var children = new List<TypeTreeNodeStruct>();
		int currentIndex = nodeIndex + 1;
		while (currentIndex < nodes.Count && nodes[currentIndex].Level > node.Level)
		{
			if (nodes[currentIndex].Level == node.Level + 1)
			{
				var child = ConvertToTypeTreeNode(nodes, stringBuffer, currentIndex);
				if (child.HasValue)
				{
					children.Add(child.Value);
				}
			}
			currentIndex++;
		}

		return new TypeTreeNodeStruct(
			typeName,
			name,
			node.Version,
			(TransferMetaFlags)node.MetaFlag,
			children.ToArray()
		);
	}

	private static string ReadString(byte[] buffer, int offset)
	{
		int length = 0;
		while (offset + length < buffer.Length && buffer[offset + length] != 0)
		{
			length++;
		}
		return System.Text.Encoding.UTF8.GetString(buffer, offset, length);
	}

	public override void WriteEditor(AssetWriter writer)
	{
		Write(writer);
	}

	public override void WriteRelease(AssetWriter writer)
	{
		Write(writer);
	}

	private void Write(AssetWriter writer)
	{
		writer.Write(Version);

		if (Version == 1)
		{
			foreach (var reference in References)
			{
				writer.Write(reference.Type.ClassName);
				writer.AlignStream();
				writer.Write(reference.Type.Namespace);
				writer.AlignStream();
				writer.Write(reference.Type.Assembly);
				writer.AlignStream();

				reference.Data?.WriteRelease(writer);
			}
			// Write TERMINUS
			writer.Write(string.Empty);
			writer.AlignStream();
			writer.Write(string.Empty);
			writer.AlignStream();
			writer.Write(string.Empty);
			writer.AlignStream();
		}
		else if (Version == 2)
		{
			writer.Write(References.Count);
			foreach (var reference in References)
			{
				writer.Write(reference.Rid);
				writer.Write(reference.Type.ClassName);
				writer.AlignStream();
				writer.Write(reference.Type.Namespace);
				writer.AlignStream();
				writer.Write(reference.Type.Assembly);
				writer.AlignStream();

				reference.Data?.WriteRelease(writer);
			}
			writer.AlignStream();
		}
	}

	public override void WalkEditor(AssetWalker walker)
	{
		// Walk the managed references registry
		if (walker.EnterAsset(this))
		{
			if (walker.EnterField(this, nameof(Version)))
			{
				walker.VisitPrimitive(Version);
				walker.ExitField(this, nameof(Version));
			}

			walker.DivideAsset(this);

			if (walker.EnterField(this, "RefIds"))
			{
				if (walker.EnterList(References))
				{
					for (int i = 0; i < References.Count; i++)
					{
						if (i > 0)
						{
							walker.DivideList(References);
						}

						var reference = References[i];
						// TODO: Walk the reference object
					}
					walker.ExitList(References);
				}
				walker.ExitField(this, "RefIds");
			}

			walker.ExitAsset(this);
		}
	}

	public override void Reset()
	{
		Version = 0;
		References.Clear();
	}

	public IUnityAssetBase DeepClone(PPtrConverter converter)
	{
		var clone = new ManagedReferencesRegistry
		{
			Version = Version
		};

		foreach (var reference in References)
		{
			var clonedRef = new ManagedReferencedObject
			{
				Rid = reference.Rid,
				Type = new ManagedTypeReference
				{
					ClassName = reference.Type.ClassName,
					Namespace = reference.Type.Namespace,
					Assembly = reference.Type.Assembly
				},
				Data = reference.Data?.DeepClone(converter)
			};
			clone.References.Add(clonedRef);
		}

		return clone;
	}
} 