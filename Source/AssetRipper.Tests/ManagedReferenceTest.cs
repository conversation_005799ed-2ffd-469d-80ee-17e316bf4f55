using AssetRipper.Assets;
using AssetRipper.Export.UnityProjects;
using AssetRipper.Import.Structure.Assembly.Serializable;
using AssetRipper.SerializationLogic;
using AssetRipper.SourceGenerated.Classes.ClassID_114;
using AssetRipper.Yaml;
using System.Text;

namespace AssetRipper.Tests;

public class ManagedReferenceTest
{
	[Test]
	public void TestManagedReferenceYamlOutput()
	{
		// Create a mock MonoBehaviour with managed references
		var monoBehaviour = CreateMockMonoBehaviourWithManagedReferences();
		
		// Create a YamlWalker to export the YAML
		var walker = new YamlWalker();
		var yamlDoc = walker.ExportYamlDocument(monoBehaviour, 12345);
		
		// Write the YAML to a string
		var yamlWriter = new YamlWriter();
		yamlWriter.AddDocument(yamlDoc);
		var sb = new StringBuilder();
		using (var writer = new StringWriter(sb))
		{
			yamlWriter.Write(writer);
		}
		
		var yamlOutput = sb.ToString();
		
		// Verify the output contains the expected structure
		Assert.That(yamlOutput, Does.Contain("rid:"));
		Assert.That(yamlOutput, Does.Contain("references:"));
		Assert.That(yamlOutput, Does.Contain("type:"));
		Assert.That(yamlOutput, Does.Contain("class:"));
		
		Console.WriteLine("Generated YAML:");
		Console.WriteLine(yamlOutput);
	}
	
	private static IMonoBehaviour CreateMockMonoBehaviourWithManagedReferences()
	{
		// This is a simplified mock implementation
		// In a real test, you would create a proper MonoBehaviour with SerializableStructure
		throw new NotImplementedException("Mock implementation needed");
	}
} 