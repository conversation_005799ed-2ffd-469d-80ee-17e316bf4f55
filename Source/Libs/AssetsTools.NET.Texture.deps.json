{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AssetsTools.NET.Texture/1.0.0": {"dependencies": {"AssetRipper.TextureDecoder": "2.1.1", "AssetsTools.NET": "1.0.0", "StbImageSharp": "2.27.13", "StbImageWriteSharp": "1.16.7"}, "runtime": {"AssetsTools.NET.Texture.dll": {}}}, "AssetRipper.TextureDecoder/2.1.1": {"runtime": {"lib/net8.0/AssetRipper.TextureDecoder.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "StbImageSharp/2.27.13": {"runtime": {"lib/netstandard2.0/StbImageSharp.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "StbImageWriteSharp/1.16.7": {"runtime": {"lib/netstandard2.0/StbImageWriteSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AssetsTools.NET/1.0.0": {"runtime": {"AssetsTools.NET.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"AssetsTools.NET.Texture/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AssetRipper.TextureDecoder/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-dB49qA4n9Wdeyninhoww5NhSldxsOCu2HQnr1abakRexQ7OtrxGZuD+ta0MtBgSYQkyvQm70gXuR+6kwo7E7ug==", "path": "assetripper.texturedecoder/2.1.1", "hashPath": "assetripper.texturedecoder.2.1.1.nupkg.sha512"}, "StbImageSharp/2.27.13": {"type": "package", "serviceable": true, "sha512": "sha512-tHnP2RHgFzWbOS96UqvRO/LYU1WmpMT1bKiig45we+rpaXacBr11Fq2IBF+MqlgyLyNXxRz18E66qr4R9YlSbg==", "path": "stbimagesharp/2.27.13", "hashPath": "stbimagesharp.2.27.13.nupkg.sha512"}, "StbImageWriteSharp/1.16.7": {"type": "package", "serviceable": true, "sha512": "sha512-NF9kOxi0iS9VunklnRKgOpAWC2knCGKRjdy7RT6XAqYNyb7LQfyPHPdl4l1fJw7TFYKrGQzQOmK1XpBONC2MoA==", "path": "stbimagewritesharp/1.16.7", "hashPath": "stbimagewritesharp.1.16.7.nupkg.sha512"}, "AssetsTools.NET/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}