﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <!--If you are willing to use Windows/MacOS native APIs you will need to create 3 projects.
    One for Windows with net7.0-windows TFM, one for MacOS with net7.0-macos and one with net7.0 TFM for Linux.-->
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.2.8" />
    <PackageReference Include="Avalonia.AvaloniaEdit" Version="11.2.0" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.2.8" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.2.8" />
    <PackageReference Include="Avalonia.Themes.Simple" Version="11.2.8" />
    <PackageReference Include="AvaloniaEdit.TextMate" Version="11.2.0" />
    <PackageReference Include="Avalonia.Desktop" Version="11.2.8" />
    <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="11.2.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TexturePlugin\TexturePlugin.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="App.axaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <Compile Update="Window.axaml.cs">
      <DependentUpon>Window.axaml</DependentUpon>
    </Compile>
  </ItemGroup>
</Project>
