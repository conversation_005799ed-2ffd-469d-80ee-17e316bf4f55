<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:tex="using:TexturePlugin.ViewModels"
             mc:Ignorable="d" d:DesignWidth="700" d:DesignHeight="350"
             x:Class="TexturePlugin.Views.ExportBatchOptionsView"
             x:DataType="tex:ExportBatchOptionsViewModel">
  <Grid Margin="10,10,10,10">
    <ComboBox VerticalAlignment="Top" Height="26"
              SelectedIndex="{Binding SelectedExportType}"
              ItemsSource="{Binding DropdownItems}" />
    <ScrollBar Minimum="0" Maximum="100" Orientation="Horizontal"
                Value="{Binding Quality}"/>
    <Grid VerticalAlignment="Bottom">
      <Grid.ColumnDefinitions>
        <ColumnDefinition Width="*"></ColumnDefinition>
        <ColumnDefinition Width="*"></ColumnDefinition>
      </Grid.ColumnDefinitions>
      <Button Grid.Column="0" Command="{Binding BtnOk_Click}">Ok</Button>
      <Button Grid.Column="1" Command="{Binding BtnCancel_Click}">Cancel</Button>
    </Grid>
  </Grid>
</UserControl>
