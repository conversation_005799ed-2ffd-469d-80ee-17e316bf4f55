﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <!--If you are willing to use Windows/MacOS native APIs you will need to create 3 projects.
    One for Windows with net7.0-windows TFM, one for MacOS with net7.0-macos and one with net7.0 TFM for Linux.-->
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <ApplicationIcon>logo-new.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="logo-new.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia.Desktop" Version="11.2.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\UABEANext4\UABEANext4.csproj" />
  </ItemGroup>
  
  <Target Name="UABEAAfterBuild" AfterTargets="AfterBuild" Condition="'$(SolutionDir)' != '*Undefined*'">
    <ItemGroup>
      <ReleaseFiles Include="$(SolutionDir)ReleaseFiles\**\*" />
    </ItemGroup>
    <Copy SourceFiles="@(ReleaseFiles)" DestinationFolder="$(OutputPath)" ContinueOnError="true" />
    <MakeDir Directories="$(OutputPath)plugins" ContinueOnError="true" />
  </Target>
  
  <Target Name="UABEAAfterPublish" AfterTargets="Publish" Condition="'$(SolutionDir)' != '*Undefined*'">
    <ItemGroup>
      <ReleaseFiles Include="$(SolutionDir)ReleaseFiles\**\*" />
    </ItemGroup>
    <Copy SourceFiles="@(ReleaseFiles)" DestinationFolder="$(PublishDir)" ContinueOnError="true" />
    <MakeDir Directories="$(PublishDir)plugins" ContinueOnError="true" />
  </Target>
</Project>
