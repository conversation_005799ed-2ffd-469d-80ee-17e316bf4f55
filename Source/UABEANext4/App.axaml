<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:sys="clr-namespace:System;assembly=netstandard"
             xmlns:local="using:UABEANext4"
             x:Class="UABEANext4.App"
             RequestedThemeVariant="Default">
  <Application.DataTemplates>
    <local:ViewLocator/>
  </Application.DataTemplates>
  <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

  <Application.Styles>
    <SimpleTheme />
    <StyleInclude Source="avares://UABEANext4/Themes/DockSimpleThemeEdit.axaml" />
    <StyleInclude Source="avares://UABEANext4/Themes/HeaderedContentControlTheme.axaml" />
    <StyleInclude Source="avares://Avalonia.Controls.DataGrid/Themes/Simple.xaml"/>
    <StyleInclude Source="avares://UABEANext4/Themes/TabTheme.axaml" />
    <StyleInclude Source="avares://UABEANext4/Themes/DataValidationStyle.axaml" />
    <!--<StyleInclude Source="avares://UABEANext4/Themes/ImprovedTabStyles.axaml"/>-->
    <StyleInclude Source="avares://AvaloniaEdit/Themes/Simple/AvaloniaEdit.xaml" />

    <Style Selector="Button">
      <Setter Property="BorderBrush" Value="#555555" />
    </Style>
    <Style Selector="Button:pointerover /template/ ContentPresenter#PART_ContentPresenter">
      <Setter Property="BorderBrush" Value="#0097fb" />
    </Style>
    <Style Selector="Button:pressed /template/ ContentPresenter#PART_ContentPresenter">
      <Setter Property="Background" Value="#0097fb" />
    </Style>
    <Style>
      <Style.Resources>
        <sys:Double x:Key="ScrollBarThumbThickness">14</sys:Double>
      </Style.Resources>
    </Style>
  </Application.Styles>
</Application>
