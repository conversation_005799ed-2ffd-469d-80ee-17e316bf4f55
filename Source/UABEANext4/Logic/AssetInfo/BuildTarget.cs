namespace UABEANext4.Logic.AssetInfo;

public enum BuildTarget
{
    StandaloneOSX = 2,
    StandaloneOSXUniversal,
    StandaloneOSXIntel,
    StandaloneWindows,
    WebPlayer,
    WebPlayerStreamed,
    Wii,
    iOS,
    PS3,
    XBOX360,
    StandaloneBroadcom,
    Android,
    StandaloneGLESEmu,
    StandaloneGLES20Emu,
    NaCl,
    StandaloneLinux,
    Flash,
    StandaloneWindows64,
    WebGL,
    WSAPlayer,
    WSAPlayerX64,
    WSAPlayerARM,
    StandaloneLinux64,
    StandaloneLinuxUniversal,
    WP8Player,
    StandaloneOSXIntel64,
    BlackBerry,
    Tizen,
    PSP2,
    PS4,
    PSM,
    XboxOne,
    SamsungTV,
    N3DS,
    WiiU,
    tvOS,
    Switch,
    Lumin,
    Stadia,
    CloudRendering,
    GameCoreXboxSeries,
    GameCoreXboxOne,
    PS5,
    EmbeddedLinux,
    QNX,
    Bratwurst
}