﻿namespace UABEANext4.Logic.Mesh
{
    public enum VertexChannelFormat
    {
        Float,
        Float16,
        Color,
        Byte,
        UInt32
    }

    public enum VertexFormatV2
    {
        Float,
        Float16,
        <PERSON>orm8,
        <PERSON>Norm8,
        <PERSON><PERSON>16,
        <PERSON><PERSON>orm16,
        <PERSON><PERSON>nt8,
        <PERSON><PERSON><PERSON>8,
        <PERSON><PERSON><PERSON>16,
        <PERSON>Int16,
        <PERSON><PERSON><PERSON>32,
        <PERSON><PERSON>nt32
    }

    public enum VertexFormatV1
    {
        Float,
        Float16,
        Color,
        UNorm8,
        SNorm8,
        UNorm16,
        <PERSON>Norm16,
        UInt8,
        SInt8,
        UInt16,
        SInt16,
        UInt32,
        SInt32
    }

    public enum ChannelTypeV3
    {
        Vertex,
        Normal,
        Tangent,
        Color,
        TexCoord0,
        TexCoord1,
        TexCoord2,
        TexCoord3,
        TexCoord4,
        TexCoord5,
        TexCoord6,
        TexCoord7,
        BlendWeight,
        BlendIndices,
    }

    public enum ChannelTypeV2
    {
        Vertex,
        Normal,
        Color,
        TexCoord0,
        TexCoord1,
        TexCoord2,
        <PERSON><PERSON>oord3,
        <PERSON><PERSON>,
    }

    public enum ChannelTypeV1
    {
        Vertex,
        Normal,
        Color,
        <PERSON>Coord0,
        <PERSON><PERSON>oord1,
        <PERSON><PERSON>,
    }
}
