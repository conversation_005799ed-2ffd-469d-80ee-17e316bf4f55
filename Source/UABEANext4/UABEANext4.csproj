﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <AvaloniaResource Include="Assets\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Assets\logo-new.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AssetRipper.TextureDecoder" Version="2.1.1" />
    <PackageReference Include="Avalonia" Version="11.2.8" />
    <PackageReference Include="Avalonia.AvaloniaEdit" Version="11.2.0" />
    <PackageReference Include="Avalonia.Controls.DataGrid" Version="11.2.8" />
    <PackageReference Include="Avalonia.Skia" Version="11.2.8" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.2.8" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.2.8" />
    <PackageReference Include="Avalonia.Themes.Simple" Version="11.2.8" />
    <PackageReference Include="AvaloniaEdit.TextMate" Version="11.2.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />

    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="11.2.3" />
    <PackageReference Include="Dock.Avalonia" Version="11.2.7" />
    <PackageReference Include="Dock.Model" Version="11.2.7" />
    <PackageReference Include="Dock.Model.Mvvm" Version="11.2.7" />
    <PackageReference Include="DynamicData" Version="9.2.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.4" />
    <PackageReference Include="Mono.Cecil" Version="0.11.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Samboy063.LibCpp2IL" Version="2022.1.0-pre-release.19" />
    <PackageReference Include="Silk.NET.OpenGL" Version="2.22.0" />
    <PackageReference Include="StbImageSharp" Version="2.30.15" />
    <PackageReference Include="StbImageWriteSharp" Version="1.16.7" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="AssetsTools.NET">
      <HintPath>..\Libs\AssetsTools.NET.dll</HintPath>
    </Reference>
    <Reference Include="AssetsTools.NET.Cpp2IL">
      <HintPath>..\Libs\AssetsTools.NET.Cpp2IL.dll</HintPath>
    </Reference>
    <Reference Include="AssetsTools.NET.MonoCecil">
      <HintPath>..\Libs\AssetsTools.NET.MonoCecil.dll</HintPath>
    </Reference>
    <Reference Include="AssetsTools.NET.Texture">
      <HintPath>..\Libs\AssetsTools.NET.Texture.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Views\Dialogs\AddExternalView.axaml.cs">
      <DependentUpon>AddExternalView.axaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Dialogs\MessageBoxView.axaml.cs">
      <DependentUpon>MessageBoxView.axaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Dialogs\VersionSelectView.axaml.cs">
      <DependentUpon>VersionSelectView.axaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Dialogs\SelectDumpView.axaml.cs">
      <DependentUpon>SelectDumpView.axaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Dialogs\BatchImportView.axaml.cs">
      <DependentUpon>BatchImportView.axaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Tools\HierarchyToolView.axaml.cs">
      <DependentUpon>HierarchyToolView.axaml</DependentUpon>
    </Compile>
    <Compile Update="Views\Tools\PreviewerToolView.axaml.cs">
      <DependentUpon>PreviewerToolView.axaml</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Plugins\DefaultPlugins\" />
  </ItemGroup>
</Project>
