<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:dlg="using:UABEANext4.ViewModels.Dialogs"
             xmlns:uac="using:UABEANext4.Converters"
             mc:Ignorable="d" d:DesignWidth="300" d:DesignHeight="170"
             x:Class="UABEANext4.Views.Dialogs.AddAssetView"
             x:DataType="dlg:AddAssetViewModel">
  <Grid Margin="10,10,10,10">
    <Grid.Resources>
      <uac:AssetsFileInstanceNameConverter x:Key="AssetsFileInstanceNameConverter" />
    </Grid.Resources>
    <Grid VerticalAlignment="Top"
          ColumnDefinitions="100, *"
          RowDefinitions="Auto, Auto, Auto, Auto">
      <Label Grid.Column="0" Grid.Row="0">File</Label>
      <ComboBox Grid.Column="1" Grid.Row="0"
                ItemsSource="{Binding Files}" SelectedItem="{Binding SelectedFile}"
                DisplayMemberBinding="{Binding Path=., Converter={StaticResource AssetsFileInstanceNameConverter}}" />
      <Label Grid.Column="0" Grid.Row="1">Path ID</Label>
      <TextBox Grid.Column="1" Grid.Row="1" Text="{Binding PathIdString}" />
      <Label Grid.Column="0" Grid.Row="2">Type name/ID</Label>
      <TextBox Grid.Column="1" Grid.Row="2" Text="{Binding TypeNameOrId}" />
      <Label Grid.Column="0" Grid.Row="3">Script</Label>
      <ComboBox Grid.Column="1" Grid.Row="3"
                IsEnabled="{Binding IsScript}" ItemsSource="{Binding Scripts}"
                SelectedIndex="{Binding SelectedScriptIndex}" />
    </Grid>
    <Grid VerticalAlignment="Bottom">
      <Grid.ColumnDefinitions>
        <ColumnDefinition Width="*"></ColumnDefinition>
        <ColumnDefinition Width="*"></ColumnDefinition>
      </Grid.ColumnDefinitions>
      <Button Grid.Column="0" Command="{Binding BtnOk_Click}">Ok</Button>
      <Button Grid.Column="1" Command="{Binding BtnCancel_Click}">Cancel</Button>
    </Grid>
  </Grid>
</UserControl>
