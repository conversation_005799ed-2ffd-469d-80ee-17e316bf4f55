<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:dlg="using:UABEANext4.ViewModels.Dialogs"
             xmlns:uac="using:UABEANext4.Converters"
             mc:Ignorable="d" d:DesignWidth="350" d:DesignHeight="170"
             x:Class="UABEANext4.Views.Dialogs.AddExternalView"
             x:DataType="dlg:AddExternalViewModel">
  <Grid Margin="10,10,10,10">
    <Grid VerticalAlignment="Top"
          ColumnDefinitions="120, *"
          RowDefinitions="Auto, Auto, Auto, Auto">
      <Label Grid.Column="0" Grid.Row="0">File name</Label>
      <TextBox Grid.Column="1" Grid.Row="0"
               Text="{Binding FileName}" />
      
      <Label Grid.Column="0" Grid.Row="1">Original file name</Label>
      <TextBox Grid.Column="1" Grid.Row="1"
               IsEnabled="{Binding HasOriginalName}"
               Text="{Binding OriginalFileName}" />
      
      <Label Grid.Column="0" Grid.Row="2">Dependency type</Label>
      <ComboBox Grid.Column="1" Grid.Row="2"
                SelectedIndex="{Binding ExternalType}">
        <ComboBoxItem Content="Normal" />
        <ComboBoxItem Content="Cached" />
        <ComboBoxItem Content="Serialized" />
        <ComboBoxItem Content="Meta" />
      </ComboBox>
      
      <Label Grid.Column="0" Grid.Row="3">GUID</Label>
      <TextBox Grid.Column="1" Grid.Row="3"
               IsEnabled="{Binding HasGuid}"
               Text="{Binding GuidString}" />
    </Grid>
    <Grid VerticalAlignment="Bottom">
      <Grid.ColumnDefinitions>
        <ColumnDefinition Width="*"></ColumnDefinition>
        <ColumnDefinition Width="*"></ColumnDefinition>
      </Grid.ColumnDefinitions>
      <Button Grid.Column="0" Command="{Binding BtnOk_Click}">Ok</Button>
      <Button Grid.Column="1" Command="{Binding BtnCancel_Click}">Cancel</Button>
    </Grid>
  </Grid>
</UserControl>
