<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:dlg="using:UABEANext4.ViewModels.Dialogs"
             mc:Ignorable="d" d:DesignWidth="700" d:DesignHeight="350"
             x:Class="UABEANext4.Views.Dialogs.BatchImportView"
             x:DataType="dlg:BatchImportViewModel">
  <Grid Margin="10,10,10,10">
    <Grid.ColumnDefinitions>
      <ColumnDefinition Width="0.6*"></ColumnDefinition>
      <ColumnDefinition Width="5"></ColumnDefinition>
      <ColumnDefinition Width="0.4*"></ColumnDefinition>
    </Grid.ColumnDefinitions>
    <Border Margin="0,0,0,30" BorderBrush="Black" BorderThickness="1" Grid.Column="0">
      <DataGrid AutoGenerateColumns="True" CanUserResizeColumns="True" IsReadOnly="True"
                ItemsSource="{Binding DataGridItems}"
                SelectedItem="{Binding DataGridSelectedItem}" />
    </Border>
    <GridSplitter Grid.Column="1" />
    <Grid Grid.Column="2">
      <ListBox Margin="0,0,0,30" ItemsSource="{Binding MatchingFilesItems}"
               SelectedIndex="{Binding MatchingFilesSelectedIndex}" />
      <Grid Margin="0,0,0,0" VerticalAlignment="Bottom">
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*" />
          <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Button Grid.Column="0" Command="{Binding BtnOk_Click}">Ok</Button>
        <Button Grid.Column="1" Command="{Binding BtnCancel_Click}">Cancel</Button>
      </Grid>
    </Grid>
  </Grid>
</UserControl>
