<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             xmlns:docs="using:UABEANext4.ViewModels.Documents"
             xmlns:uac="using:UABEANext4.Converters"
             xmlns:ws="using:UABEANext4.AssetWorkspace"
             x:Class="UABEANext4.Views.Documents.AssetDocumentView"
             x:DataType="docs:AssetDocumentViewModel">
  <Grid>
    <Grid.Resources>
      <uac:AssetTypeIconConverter x:Key="AssetTypeIconConverter" />
    </Grid.Resources>
    <Grid.RowDefinitions>
      <RowDefinition Height="25"/>
      <RowDefinition Height="26"/>
      <RowDefinition Height="*"/>
    </Grid.RowDefinitions>

    <UniformGrid Grid.Row="0" Rows="1" HorizontalAlignment="Left">
      <Button MaxWidth="100" Command="{Binding ViewScene}">
        <StackPanel Orientation="Horizontal">
          <Image Source="avares://UABEANext4/Assets/Icons/action-view-scene.png" Margin="0,0,3,0" />
          <TextBlock>View Scene</TextBlock>
        </StackPanel>
      </Button>
      <Button MaxWidth="100" Command="{Binding Export}">
        <StackPanel Orientation="Horizontal">
          <Image Source="avares://UABEANext4/Assets/Icons/action-export-asset.png" Margin="0,0,3,0" />
          <TextBlock>Export</TextBlock>
        </StackPanel>
      </Button>
      <Button MaxWidth="100" Command="{Binding Import}">
        <StackPanel Orientation="Horizontal">
          <Image Source="avares://UABEANext4/Assets/Icons/action-import-asset.png" Margin="0,0,3,0" />
          <TextBlock>Import</TextBlock>
        </StackPanel>
      </Button>
      <Button MaxWidth="100" Command="{Binding EditDump}">
        <StackPanel Orientation="Horizontal">
          <Image Source="avares://UABEANext4/Assets/Icons/action-view-info.png" Margin="0,0,3,0" />
          <TextBlock>Edit Data</TextBlock>
        </StackPanel>
      </Button>
      <Button MaxWidth="100" Command="{Binding ShowPlugins}" Name="showPluginsBtn">
        <StackPanel Orientation="Horizontal">
          <Image Source="avares://UABEANext4/Assets/Icons/action-plugins.png" Margin="0,0,3,0" />
          <TextBlock>Plugins</TextBlock>
        </StackPanel>
        <Button.Flyout>
          <MenuFlyout ItemsSource="{Binding PluginsItems}">
            <MenuFlyout.ItemContainerTheme>
              <ControlTheme TargetType="MenuItem" BasedOn="{StaticResource {x:Type MenuItem}}" x:DataType="docs:PluginItemInfo">
                <Setter Property="Header" Value="{Binding Name}"/>
                <Setter Property="Command" Value="{Binding Execute}"/>
                <Setter Property="CommandParameter" Value="{Binding $parent[UserControl].((docs:AssetDocumentViewModel)DataContext).SelectedItems}"/>
              </ControlTheme>
            </MenuFlyout.ItemContainerTheme>
          </MenuFlyout>
          <!--<MenuFlyout>
            <ItemsControl ItemsSource="{Binding PluginsItems}">
              <ItemsControl.ItemTemplate>
                <DataTemplate>
                  <MenuItem Header="{Binding}" Command="{Binding $parent[UserControl].((docs:AssetDocumentViewModel)DataContext).PluginItemClicked}" CommandParameter="{Binding}" />
                </DataTemplate>
              </ItemsControl.ItemTemplate>
            </ItemsControl>
          </MenuFlyout>-->
        </Button.Flyout>
      </Button>
      <Button MaxWidth="100" Command="{Binding AddAsset}">
        <StackPanel Orientation="Horizontal">
          <Image Source="avares://UABEANext4/Assets/Icons/action-add-asset.png" Margin="0,0,3,0" />
          <TextBlock>Add</TextBlock>
        </StackPanel>
      </Button>
      <Button MaxWidth="100">
        <StackPanel Orientation="Horizontal">
          <Image Source="avares://UABEANext4/Assets/Icons/action-remove-asset.png" Margin="0,0,3,0" />
          <TextBlock>Remove</TextBlock>
        </StackPanel>
      </Button>
    </UniformGrid>

    <Grid Grid.Row="1" ColumnDefinitions="80,10,*">
      <Label VerticalContentAlignment="Center" Grid.Column="0">Search: </Label>
      <TextBox Grid.Column="2" Text="{Binding SearchText}" />
    </Grid>

    <DataGrid
      Grid.Row="2" AutoGenerateColumns="False" CanUserResizeColumns="True"
      IsReadOnly="True" ItemsSource="{Binding CollectionView}" x:DataType="docs:AssetDocumentViewModel"
      SelectionChanged="DataGrid_SelectionChanged" Margin="0,0,0,0" Name="dataGrid">
      <DataGrid.RowTheme>
        <ControlTheme TargetType="DataGridRow" BasedOn="{StaticResource {x:Type DataGridRow}}">
          <ControlTheme.Children>
            <Style Selector="^:nth-child(2n)">
              <Setter Property="Background" Value="#00FFFFFF" />
            </Style>
          </ControlTheme.Children>
        </ControlTheme>
      </DataGrid.RowTheme>
      <DataGrid.Columns>
        <DataGridTemplateColumn Header=" ">
            <DataGridTemplateColumn.CellTemplate>
                <DataTemplate x:DataType="ws:AssetInst">
                    <Image Source="{Binding Type, Converter={StaticResource AssetTypeIconConverter}}" Width="16" Height="16" />
                </DataTemplate>
            </DataGridTemplateColumn.CellTemplate>
        </DataGridTemplateColumn>
        
        <DataGridTextColumn Header="Name" Width="150" Binding="{Binding DisplayName}" x:DataType="ws:AssetInst"/>
        <!--<DataGridTextColumn Header="Container" Binding="{Binding Container}" x:DataType="ws:AssetInst"/>-->
        <DataGridTextColumn Header="Type" Width="120" Binding="{Binding Type}" x:DataType="ws:AssetInst"/>
        <DataGridTextColumn Header="File Name" Width="120" Binding="{Binding FileName}" x:DataType="ws:AssetInst"/>
        <DataGridTextColumn Header="Path ID" Width="130" Binding="{Binding PathId}" x:DataType="ws:AssetInst"/>
        <!--<DataGridTextColumn Header="Offset" Binding="{Binding ByteStart}" x:DataType="ws:AssetInst"/>-->
        <DataGridTextColumn Header="Size" Width="80" Binding="{Binding ByteSizeModified}" x:DataType="ws:AssetInst"/>
        <DataGridTextColumn Header="Modified" Binding="{Binding ModifiedString}" x:DataType="ws:AssetInst"/>
      </DataGrid.Columns>
    </DataGrid>
  </Grid>
</UserControl>
