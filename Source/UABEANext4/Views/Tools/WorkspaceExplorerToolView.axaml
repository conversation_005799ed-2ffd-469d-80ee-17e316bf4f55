<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:uaw="using:UABEANext4.AssetWorkspace"
             xmlns:tools="using:UABEANext4.ViewModels.Tools"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="UABEANext4.Views.Tools.WorkspaceExplorerToolView"
             x:DataType="tools:WorkspaceExplorerToolViewModel">
  <UserControl.KeyBindings>
    <KeyBinding Gesture="F2" Command="{Binding RenameItem}" />
  </UserControl.KeyBindings>
  <TreeView Margin="0,0,0,0" Background="#252526" Name="SolutionTreeView"
            ItemsSource="{Binding Workspace.RootItems}" SelectedItems="{Binding SelectedItems}"
            SelectionMode="Multiple">
    <TreeView.ItemTemplate>
      <TreeDataTemplate DataType="{x:Type uaw:WorkspaceItem}" ItemsSource="{Binding Children}">
        <TextBlock Text="{Binding Name}" Foreground="{Binding Color}" />
      </TreeDataTemplate>
    </TreeView.ItemTemplate>
  </TreeView>
</UserControl>
